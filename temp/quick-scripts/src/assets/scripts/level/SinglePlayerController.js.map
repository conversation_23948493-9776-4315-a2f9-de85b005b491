{"version": 3, "sources": ["assets/scripts/level/SinglePlayerController.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAA2D;AAC3D,8CAA6C;AAC7C,qDAAkD;AAClD,6CAA4C;AAE5C,0DAAuD;AAEjD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C;;;GAGG;AAEH;IAA4C,0CAAY;IAAxD;QAAA,qEA+nCC;QA7nCG,YAAY;QAEZ,kBAAY,GAAY,IAAI,CAAC,CAAC,UAAU;QAGxC,kBAAY,GAAY,IAAI,CAAC,CAAC,UAAU;QAGxC,kBAAY,GAAY,IAAI,CAAC,CAAC,UAAU;QAGxC,mBAAa,GAAY,IAAI,CAAC,CAAC,WAAW;QAG1C,oBAAc,GAAY,IAAI,CAAC,CAAC,YAAY;QAE5C,YAAY;QACJ,sBAAgB,GAAY,IAAI,CAAC;QAGzC,gBAAU,GAAc,IAAI,CAAC,CAAC,QAAQ;QAGtC,kBAAY,GAAc,IAAI,CAAC,CAAC,QAAQ;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAExC,OAAO;QACC,gBAAU,GAAG;YACjB,KAAK,EAAE;gBACH,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;aACxC;YACD,KAAK,EAAE;gBACH,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;aACxC;YACD,KAAK,EAAE;gBACH,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;aACxC;YACD,MAAM,EAAE;gBACJ,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;aACzC;YACD,OAAO,EAAE;gBACL,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;gBACnC,eAAe,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;aAC1C;SACJ,CAAC;QAEF,SAAS;QACD,sBAAgB,GAAQ,IAAI,CAAC;QAC7B,oBAAc,GAAW,KAAK,CAAC,CAAC,QAAQ;QAEhD,SAAS;QACD,cAAQ,GAAY,EAAE,CAAC;QACvB,eAAS,GAAgB,EAAE,CAAC;;IAyiCxC,CAAC;IAriCG,uCAAM,GAAN;QACI,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QAEtF,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAED,sCAAK,GAAL;QAAA,iBAKC;QAJG,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC;YACd,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,0CAAS,GAAT;QACI,SAAS;QACT,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACI,2CAAU,GAAjB,UAAkB,OAAe;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEjD,kBAAkB;YAClB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAIlC,UAAU;YACV,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,SAAS,EAAE,CAAC;aACpB;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,uDAAa,OAAS,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACK,oDAAmB,GAA3B,UAA4B,OAAe;QAGvC,QAAQ,OAAO,EAAE;YACb,KAAK,KAAK;gBACN,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC;gBAE1C,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC;gBAE1C,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC;gBAE1C,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC;gBAE3C,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;gBAE5C,MAAM;YACV;gBACI,OAAO,CAAC,KAAK,CAAC,wDAAc,OAAS,CAAC,CAAC;gBACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,MAAM;SACb;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,uGAAqB,OAAO,4DAAY,CAAC,CAAC;SAC3D;IACL,CAAC;IAED;;;OAGG;IACI,6CAAY,GAAnB,UAAoB,SAAkB;QAClC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;SACpB;IACL,CAAC;IAED;;OAEG;IACK,0CAAS,GAAjB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxB,OAAO;SACV;QAEK,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAE7D,oBAAoB;QACpB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;oBAClB,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,UAAU,EAAE,KAAK;oBACjB,SAAS,EAAE,KAAK;oBAChB,MAAM,EAAE,KAAK;oBACb,MAAM,EAAE,CAAC;iBACZ,CAAC;gBACF,uDAAuD;aAC1D;SACJ;QAED,8BAA8B;QAE9B,8BAA8B;QAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;IAE3B,CAAC;IAED;;OAEG;IACK,gDAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,8BAA8B;QAC9B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,4DAA2B,GAAnC;QAGI,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACrC,OAAO;SACV;QAID,yBAAyB;QACzB,IAAI,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAI9C,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,wBAAwB;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,MAAM,EAAE;gBAER,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC3D,gBAAgB;gBAChB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC3C,iBAAiB,EAAE,CAAC;aACvB;iBAAM;gBACH,8BAA8B;gBAC9B,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,aAAa,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;gBAC5D,IAAI,aAAa,EAAE;oBAEf,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;oBACzE,gBAAgB;oBAChB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBACxE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;oBACzD,iBAAiB,EAAE,CAAC;iBACvB;qBAAM;iBAEN;aACJ;SACJ;IAGL,CAAC;IAID;;OAEG;IACK,4DAA2B,GAAnC,UAAoC,QAAgB;QAChD,mBAAmB;QACnB,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACjD,IAAI,KAAK,EAAE;YACP,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3D;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,8DAA6B,GAArC,UAAsC,GAAY;QAC9C,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,IAAI,CAAC;QAExC,sBAAsB;QACtB,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,IAAM,WAAW,GAAG,GAAG,CAAC,CAAG,iBAAiB;YAC5C,IAAM,YAAY,GAAG,GAAG,CAAC,CAAE,iBAAiB;YAC5C,IAAM,SAAS,GAAG,EAAE,CAAC,CAAM,YAAY;YAEvC,IAAI,GAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;YAC1D,IAAI,GAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;YAE3D,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAC,EAAE,GAAC,CAAC,EAAE;gBAC9B,OAAO,EAAC,CAAC,EAAE,GAAC,EAAE,CAAC,EAAE,GAAC,EAAC,CAAC;aACvB;YACD,OAAO,IAAI,CAAC;SACf;QAED,oBAAoB;QACd,IAAA,KAA0B,IAAI,CAAC,gBAAgB,EAA7C,QAAQ,cAAA,EAAE,SAAS,eAA0B,CAAC;QACtD,IAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;QACnC,IAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,IAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;QACjC,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAE3D,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9B,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,qDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,IAAA,KAA0B,IAAI,CAAC,gBAAgB,EAA7C,QAAQ,cAAA,EAAE,SAAS,eAA0B,CAAC;QACtD,IAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;QACnC,IAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,IAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;QACjC,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEnC,YAAY;QACZ,0BAA0B;QAC1B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAChE,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QAEnE,OAAO,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,2DAA0B,GAAlC,UAAmC,QAAiB,EAAE,CAAS,EAAE,CAAS;QAA1E,iBAiEC;QA9DG,SAAS;QACT,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,iBAAiB,GAAa,IAAI,CAAC;QACvC,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,SAAS;QAEtC,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,MAA2B;YACnE,cAAc,GAAG,IAAI,CAAC;YACtB,cAAc,GAAG,CAAC,CAAC;YAEnB,SAAS;YACT,iBAAiB,GAAG;gBAChB,IAAI,cAAc,EAAE;oBAChB,cAAc,IAAI,GAAG,CAAC;oBACtB,IAAI,cAAc,IAAI,eAAe,EAAE;wBACnC,KAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC3B,cAAc,GAAG,KAAK,CAAC;wBACvB,IAAI,iBAAiB,EAAE;4BACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;yBACtC;qBACJ;iBACJ;YACL,CAAC,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAChE,iBAAiB;YACjB,IAAI,cAAc,IAAI,cAAc,GAAG,eAAe,EAAE;gBACpD,KAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACjC;YAED,SAAS;YACT,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,MAA2B;YACpE,SAAS;YACT,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,8BAA8B;QAC9B,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YAET,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;SAC3B;aAAM;SAEN;IAGL,CAAC;IAED;;OAEG;IACK,4CAAW,GAAnB,UAAoB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAGlE,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,uCAAY,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACrC,OAAO;SACV;QAED,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,yDAAe,CAAC,UAAK,CAAC,uBAAkB,QAAQ,CAAC,UAAU,qBAAgB,QAAQ,CAAC,SAAW,CAAC,CAAC;YAC9G,OAAO;SACV;QAGD,YAAY;QACZ,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,gDAAe,GAAvB,UAAwB,CAAS,EAAE,CAAS;QAGxC,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,uCAAY,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACrC,OAAO;SACV;QAED,kBAAkB;QAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC,SAAS,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,uFAAoB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YAC7C,OAAO;SACV;QAGD,sBAAsB;QACtB,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,kDAAiB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAC1C,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAC;QACnC,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAC7D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,oDAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC5D,IAAM,SAAS,GAA2B;YACtC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,MAAM,CAAC,qBAAqB;SACvC,CAAC;QAGF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACK,iDAAgB,GAAxB,UAAyB,IAAS;QAC9B,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAS,CAAC,sBAAsB,EAAE;YACjD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACjD;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAS,CAAC,mBAAmB,EAAE;YACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACtC;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAS,CAAC,gBAAgB,EAAE;YAClD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnC;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,qBAAS,CAAC,sBAAsB,EAAE;YACxD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;OAEG;IACK,8DAA6B,GAArC,UAAsC,YAAiB;QAAvD,iBAuEC;QApEW,IAAA,MAAM,GAAmF,YAAY,OAA/F,EAAE,gBAAgB,GAAiE,YAAY,iBAA7E,EAAE,UAAU,GAAqD,YAAY,WAAjE,EAAE,OAAO,GAA4C,YAAY,QAAxD,EAAE,cAAc,GAA4B,YAAY,eAAxC,EAAE,OAAO,GAAmB,YAAY,QAA/B,EAAE,CAAC,GAAgB,YAAY,EAA5B,EAAE,CAAC,GAAa,YAAY,EAAzB,EAAE,MAAM,GAAK,YAAY,OAAjB,CAAkB;QAE9G,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChC,OAAO;SACV;QAID,IAAI,MAAM,KAAK,CAAC,EAAE;YACd,OAAO;YAEP,gCAAgC;YAChC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;gBAE5D,IAAI,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aACjD;YAED,aAAa;YACb,IAAI,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;gBAGrD,kCAAkC;gBAClC,gBAAgB,CAAC,OAAO,CAAC,UAAC,UAAe,EAAE,KAAa;oBAEpD,yBAAyB;oBACzB,IAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;oBAC3B,IAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;oBAC3B,IAAM,SAAS,GAAG,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;wBACpD,UAAU,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;4BACnE,UAAU,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;oBAEjF,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,EAAE;wBAC5C,OAAO,CAAC,IAAI,CAAC,8CAAW,EAAE,UAAU,CAAC,CAAC;wBACtC,OAAO;qBACV;oBAED,IAAI,SAAS,KAAK,SAAS,EAAE;wBACzB,OAAO,CAAC,IAAI,CAAC,wCAAU,EAAE,UAAU,CAAC,CAAC;wBACrC,OAAO;qBACV;oBAED,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;wBACvC,OAAO,CAAC,IAAI,CAAC,uCAAY,KAAK,UAAK,KAAK,MAAG,CAAC,CAAC;wBAC7C,OAAO;qBACV;oBAED,wBAAwB;oBACxB,KAAI,CAAC,YAAY,CAAC;wBACd,KAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC7D,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa;gBAClC,CAAC,CAAC,CAAC;aACN;SACJ;aAAM,IAAI,MAAM,KAAK,CAAC,EAAE;YACrB,kBAAkB;YAClB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACjC;SACJ;QAED,SAAS;QACT,IAAI,UAAU,KAAK,CAAC,EAAE;SAErB;aAAM,IAAI,UAAU,KAAK,CAAC,EAAE;SAE5B;IACL,CAAC;IAED;;OAEG;IACK,mDAAkB,GAA1B,UAA2B,IAAS;QAGxB,IAAA,OAAO,GAAgD,IAAI,QAApD,EAAE,SAAS,GAAqC,IAAI,UAAzC,EAAE,QAAQ,GAA2B,IAAI,SAA/B,EAAE,OAAO,GAAkB,IAAI,QAAtB,EAAE,WAAW,GAAK,IAAI,YAAT,CAAU;QAEpE,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,WAAW;QACX,IAAI,SAAS,EAAE;YAGX,IAAI,WAAW,EAAE;aAEhB;SACJ;aAAM;SAEN;IACL,CAAC;IAED;;OAEG;IACK,sDAAqB,GAA7B,UAA8B,IAAS;QAGnC,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,SAAS;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;IAG1B,CAAC;IAED;;OAEG;IACK,gDAAe,GAAvB,UAAwB,IAAS;QAE7B,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,+BAA+B;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAID;;OAEG;IACK,mDAAkB,GAA1B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC/B,OAAO;SACV;QAED,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEjC,gCAAgC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhD,0BAA0B;YAC1B,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC1D,UAAU,EAAE,CAAC;gBAEb,qBAAqB;gBACrB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAE/F,IAAI,SAAS,EAAE;oBACX,OAAO,CAAC,GAAG,CAAC,+BAAS,KAAK,CAAC,IAAM,CAAC,CAAC;oBACnC,aAAa,EAAE,CAAC;iBACnB;qBAAM;oBACH,mBAAmB,EAAE,CAAC;iBACzB;gBAED,0BAA0B;gBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,mBAAmB;gBACnB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEjB,qBAAqB;gBACrB,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;iBACzB;aACJ;SACJ;QAED,OAAO,CAAC,GAAG,CAAC,8DAAe,UAAU,4BAAQ,aAAa,kCAAS,mBAAmB,WAAG,CAAC,CAAC;QAE3F,WAAW;QACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,oDAAmB,GAA3B;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO;SACV;QAEK,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;QAE7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;oBACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;iBACzC;aACJ;SACJ;QAED,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,wDAAuB,GAA/B;QAGI,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QACT,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,SAAS;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,oCAAoC;QACpC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAExC,+CAA+C;QAC/C,wDAAwD;QAExD,2BAA2B;QAC3B,IAAM,eAAe,GAAG;YACpB,OAAO,EAAE,+BAAa,CAAC,YAAY;YACnC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,gBAAgB;SACzC,CAAC;QAEF,aAAa;QACb,iBAAO,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAE3D,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACK,2DAA0B,GAAlC,UAAmC,CAAS,EAAE,CAAS,EAAE,KAAU;QAAnE,iBA0CC;QAvCG,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC/B,OAAO,CAAC,KAAK,CAAC,uCAAY,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACtC,OAAO;SACV;QAED,UAAU;QACV,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACvC,OAAO,CAAC,KAAK,CAAC,6CAAa,CAAC,UAAK,CAAC,mBAAS,KAAO,CAAC,CAAC;YACpD,OAAO;SACV;QAED,SAAS;QACT,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;QAC3B,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAE1B,gBAAgB;QAChB,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAExB,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC;YAGd,UAAU;YACV,IAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE7E,IAAI,KAAK,KAAK,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE;gBACrC,OAAO;gBACP,KAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC/B;iBAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACvC,OAAO;gBACP,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;aAC3C;iBAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;aAE1B;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,wDAAc,KAAK,8BAAU,QAAQ,cAAS,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;aAC1E;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,6CAAY,GAApB,UAAqB,CAAS,EAAE,CAAS;QAGrC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE;YAEV,SAAS;YACT,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;iBACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACvD,IAAI,CAAC;gBACF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YAE5B,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;SAChB;aAAM;YACH,OAAO,CAAC,KAAK,CAAC,yDAAe,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;aAEtB;SACJ;IACL,CAAC;IAED;;OAEG;IACK,wDAAuB,GAA/B,UAAgC,CAAS,EAAE,CAAS;QAChD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3B,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACtB;QAED,kBAAkB;QAClB,QAAQ,IAAI,CAAC,cAAc,EAAE;YACzB,KAAK,KAAK;gBACN,kBAAkB;gBAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,KAAK,KAAK;gBACN,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,KAAK,KAAK;gBACN,2BAA2B;gBAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,KAAK,MAAM;gBACP,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,KAAK,OAAO;gBACR,4BAA4B;gBAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7C;gBACI,OAAO,CAAC,KAAK,CAAC,uDAAa,IAAI,CAAC,cAAgB,CAAC,CAAC;gBAClD,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;OAEG;IACK,qDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,yDAAyD;QACzD,uBAAuB;QACvB,iCAAiC;QACjC,iCAAiC;QACjC,qBAAqB;QAErB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAC9B,IAAM,KAAK,GAAG,EAAE,CAAC,CAAK,QAAQ;QAE9B,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAEpC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,qDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,wBAAwB;QACxB,IAAM,UAAU,GAAG,GAAG,CAAC;QACvB,IAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,iBAAiB;QACjB,IAAM,MAAM,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACnD,IAAM,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAErD,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,qDAAoB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,wBAAwB;QACxB,IAAM,UAAU,GAAG,GAAG,CAAC;QACvB,IAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAM,MAAM,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACnD,IAAM,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAErD,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,sDAAqB,GAA7B,UAA8B,CAAS,EAAE,CAAS;QAC9C,wBAAwB;QACxB,IAAM,UAAU,GAAG,GAAG,CAAC;QACvB,IAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAM,MAAM,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACnD,IAAM,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAErD,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,uDAAsB,GAA9B,UAA+B,CAAS,EAAE,CAAS;QAC/C,uBAAuB;QACvB,IAAM,UAAU,GAAG,GAAG,CAAC;QACvB,IAAM,WAAW,GAAG,GAAG,CAAC;QACxB,IAAM,SAAS,GAAG,EAAE,CAAC;QACrB,IAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAM,MAAM,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;QACnD,IAAM,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAErD,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACxC,IAAM,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAEzC,OAAO,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,iDAAgB,GAAxB,UAAyB,CAAS,EAAE,CAAS;QACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,WAAW;QACX,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;QAEvB,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,sBAAsB;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAC5C;QAED,SAAS;QACT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;aACrC,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;OAEG;IACK,mDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;QAE3B,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,sBAAsB;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC9C;QAED,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;OAEG;IACK,mDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC3D,eAAe;QACf,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC;gBACI,OAAO,CAAC,KAAK,CAAC,2CAAW,MAAQ,CAAC,CAAC;gBACnC,OAAO;SACd;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,SAAO,MAAM,sGAAwB,CAAC,CAAC;YACrD,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,SAAO,MAAQ,CAAC;QAElC,OAAO;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,sBAAsB;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC9C;QAED,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IAGjB,CAAC;IAED;;OAEG;IACI,gDAAe,GAAtB;QACI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,IAAM,gBAAgB,GAAc,EAAE,CAAC;QACvC,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChD,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAE5B,+BAA+B;YAC/B,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;gBAC9B,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAC/B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACvB,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3B,QAAQ,KAAK,iBAAiB,CAAC,CAAC,WAAW;YAE5D,gCAAgC;YAChC,IAAM,QAAQ,GAAG,QAAQ,KAAK,MAAM;gBACrB,QAAQ,KAAK,QAAQ;gBACrB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;gBAC3B,QAAQ,KAAK,SAAS;gBACtB,QAAQ,KAAK,WAAW,CAAC;YAExC,IAAI,QAAQ,IAAI,CAAC,UAAU,EAAE;gBACzB,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7B,YAAY,EAAE,CAAC;aAElB;SACJ;QAED,aAAa;QACb,gBAAgB,CAAC,OAAO,CAAC,UAAA,KAAK;YAC1B,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IAGP,CAAC;IAED;;OAEG;IACI,+CAAc,GAArB;QACI,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QACT,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACjB,IAAA,KAAiB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAApD,IAAI,UAAA,EAAE,IAAI,UAA0C,CAAC;YAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;oBAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;wBACzC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;wBACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC;wBACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;wBACnC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;qBAClC;oBAED,SAAS;oBACT,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,IAAI,QAAQ,EAAE;wBACV,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;wBACvB,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBAC3B;iBACJ;aACJ;SACJ;IAGL,CAAC;IAvnCD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;gEACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;iEACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;kEACa;IAM/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;8DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACU;IAjDrB,sBAAsB;QADlC,OAAO;OACK,sBAAsB,CA+nClC;IAAD,6BAAC;CA/nCD,AA+nCC,CA/nC2C,EAAE,CAAC,SAAS,GA+nCvD;AA/nCY,wDAAsB", "file": "", "sourceRoot": "/", "sourcesContent": ["import { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { EventType } from \"../common/EventCenter\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { LevelClickBlockRequest, LevelClickBlockResponse } from \"../bean/GameBean\";\nimport { AutoMessageId } from \"../net/MessageBaseBean\";\n\nconst { ccclass, property } = cc._decorator;\n\n/**\n * 单机模式控制器\n * 负责处理单机扫雷游戏的逻辑\n */\n@ccclass\nexport class SinglePlayerController extends cc.Component {\n\n    // 不同大小的棋盘节点\n    @property(cc.Node)\n    board8x8Node: cc.Node = null; // 8x8棋盘节点\n\n    @property(cc.Node)\n    board8x9Node: cc.Node = null; // 8x9棋盘节点\n\n    @property(cc.Node)\n    board9x9Node: cc.Node = null; // 9x9棋盘节点\n\n    @property(cc.Node)\n    board9x10Node: cc.Node = null; // 9x10棋盘节点\n\n    @property(cc.Node)\n    board10x10Node: cc.Node = null; // 10x10棋盘节点\n\n    // 当前激活的棋盘节点\n    private currentBoardNode: cc.Node = null;\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null; // 炸弹预制体\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null; // 标记预制体\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null; // 数字1预制体\n\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null; // 数字2预制体\n\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null; // 数字3预制体\n\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null; // 数字4预制体\n\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null; // 数字5预制体\n\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null; // 数字6预制体\n\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null; // 数字7预制体\n\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null; // 数字8预制体\n\n    // 地图配置\n    private mapConfigs = {\n        \"8x8\": {\n            boardSize: { width: 752, height: 752 },\n            gridSize: { width: 88, height: 88 },\n            boardDimensions: { rows: 8, cols: 8 }\n        },\n        \"8x9\": {\n            boardSize: { width: 752, height: 845 },\n            gridSize: { width: 88, height: 88 },\n            boardDimensions: { rows: 8, cols: 9 }\n        },\n        \"9x9\": {\n            boardSize: { width: 752, height: 747 },\n            gridSize: { width: 76, height: 76 },\n            boardDimensions: { rows: 9, cols: 9 }\n        },\n        \"9x10\": {\n            boardSize: { width: 752, height: 830 },\n            gridSize: { width: 78, height: 78 },\n            boardDimensions: { rows: 9, cols: 10 }\n        },\n        \"10x10\": {\n            boardSize: { width: 752, height: 745 },\n            gridSize: { width: 69, height: 69 },\n            boardDimensions: { rows: 10, cols: 10 }\n        }\n    };\n\n    // 当前地图配置\n    private currentMapConfig: any = null;\n    private currentMapType: string = \"8x8\"; // 默认8x8\n\n    // 格子数据存储\n    private gridData: any[][] = [];\n    private gridNodes: cc.Node[][] = [];\n\n\n\n    onLoad() {\n        // 注册消息监听\n        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n\n        // 设置默认地图配置\n        this.setMapType(this.currentMapType);\n        this.initBoard();\n    }\n\n    start() {\n        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成\n        this.scheduleOnce(() => {\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n    }\n\n    onDestroy() {\n        // 取消消息监听\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 设置地图类型\n     * @param mapType 地图类型 \"8x8\", \"8x9\", \"9x9\", \"9x10\", \"10x10\"\n     */\n    public setMapType(mapType: string) {\n        if (this.mapConfigs[mapType]) {\n            this.currentMapType = mapType;\n            this.currentMapConfig = this.mapConfigs[mapType];\n\n            // 根据地图类型设置对应的棋盘节点\n            this.setCurrentBoardNode(mapType);\n\n           \n\n            // 重新初始化棋盘\n            if (this.currentBoardNode) {\n                this.initBoard();\n            }\n        } else {\n            console.error(`不支持的地图类型: ${mapType}`);\n        }\n    }\n\n    /**\n     * 根据地图类型设置当前棋盘节点\n     */\n    private setCurrentBoardNode(mapType: string) {\n     \n\n        switch (mapType) {\n            case \"8x8\":\n                this.currentBoardNode = this.board8x8Node;\n            \n                break;\n            case \"8x9\":\n                this.currentBoardNode = this.board8x9Node;\n               \n                break;\n            case \"9x9\":\n                this.currentBoardNode = this.board9x9Node;\n               \n                break;\n            case \"9x10\":\n                this.currentBoardNode = this.board9x10Node;\n              \n                break;\n            case \"10x10\":\n                this.currentBoardNode = this.board10x10Node;\n               \n                break;\n            default:\n                console.error(`❌ 未知的地图类型: ${mapType}`);\n                this.currentBoardNode = null;\n                break;\n        }\n\n        if (!this.currentBoardNode) {\n            console.error(`❌ 棋盘节点未配置！请在编辑器中为 ${mapType} 配置对应的棋盘节点`);\n        }\n    }\n\n    /**\n     * 设置棋盘节点（兼容旧接口，建议使用setMapType）\n     * @param boardNode 棋盘节点\n     */\n    public setBoardNode(boardNode: cc.Node) {\n        this.currentBoardNode = boardNode;\n        if (this.currentMapConfig) {\n            this.initBoard();\n        }\n    }\n\n    /**\n     * 初始化棋盘（完全仿照联机模式）\n     */\n    private initBoard() {\n        if (!this.currentMapConfig) {\n            console.warn(\"地图配置未设置\");\n            return;\n        }\n\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n\n        // 初始化数据数组（完全仿照联机模式）\n        this.gridData = [];\n        this.gridNodes = [];\n\n        for (let x = 0; x < cols; x++) {\n            this.gridData[x] = [];\n            this.gridNodes[x] = [];\n            for (let y = 0; y < rows; y++) {\n                this.gridData[x][y] = {\n                    x: x,\n                    y: y,\n                    isRevealed: false,\n                    hasPlayer: false,\n                    isMine: false,\n                    number: 0\n                };\n                // 注意：gridNodes[x][y] 在 enableTouchForExistingGrids 中设置\n            }\n        }\n\n        // 不再使用预制体容器，完全仿照联机模式直接添加到棋盘节点\n\n        // 调用createGridNodes（完全仿照联机模式）\n        this.createGridNodes();\n\n    }\n\n    /**\n     * 启用现有格子的触摸事件（完全仿照联机模式）\n     */\n    private createGridNodes() {\n        if (!this.currentBoardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件（完全仿照联机模式）\n        this.enableTouchForExistingGrids();\n    }\n\n    /**\n     * 为现有格子启用触摸事件（完全仿照联机模式）\n     */\n    private enableTouchForExistingGrids() {\n        \n\n        // 检查棋盘节点是否存在（完全仿照联机模式）\n        if (!this.currentBoardNode) {\n            console.error(\"❌ 棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n      \n\n        // 遍历棋盘节点的所有子节点（完全仿照联机模式）\n        let children = this.currentBoardNode.children;\n\n     \n\n        let touchEnabledCount = 0;\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n\n            // 尝试从节点名称解析坐标（完全仿照联机模式）\n            let coords = this.parseGridCoordinateFromName(child.name);\n            if (coords) {\n                \n                this.setupSingleGridTouchEvents(child, coords.x, coords.y);\n                // 完全仿照联机模式的存储方式\n                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];\n                this.gridNodes[coords.x][coords.y] = child;\n                touchEnabledCount++;\n            } else {\n                // 如果无法从名称解析，尝试从位置计算（完全仿照联机模式）\n                let pos = child.getPosition();\n                let coordsFromPos = this.getGridCoordinateFromPosition(pos);\n                if (coordsFromPos) {\n                 \n                    this.setupSingleGridTouchEvents(child, coordsFromPos.x, coordsFromPos.y);\n                    // 完全仿照联机模式的存储方式\n                    this.gridNodes[coordsFromPos.x] = this.gridNodes[coordsFromPos.x] || [];\n                    this.gridNodes[coordsFromPos.x][coordsFromPos.y] = child;\n                    touchEnabledCount++;\n                } else {\n              \n                }\n            }\n        }\n\n       \n    }\n\n\n\n    /**\n     * 从节点名称解析格子坐标\n     */\n    private parseGridCoordinateFromName(nodeName: string): {x: number, y: number} | null {\n        // 尝试匹配 Grid_x_y 格式\n        const match = nodeName.match(/Grid_(\\d+)_(\\d+)/);\n        if (match) {\n            return { x: parseInt(match[1]), y: parseInt(match[2]) };\n        }\n        return null;\n    }\n\n    /**\n     * 从位置计算格子坐标（完全仿照联机模式）\n     */\n    private getGridCoordinateFromPosition(pos: cc.Vec2): {x: number, y: number} | null {\n        if (!this.currentMapConfig) return null;\n\n        // 对于8x8地图，使用正确的单机模式配置\n        if (this.currentMapType === \"8x8\") {\n            const BOARD_WIDTH = 752;   // 8x8棋盘大小752x752\n            const BOARD_HEIGHT = 752;  // 8x8棋盘大小752x752\n            const GRID_SIZE = 88;      // 格子大小88x88\n\n            let x = Math.floor((pos.x + BOARD_WIDTH / 2) / GRID_SIZE);\n            let y = Math.floor((pos.y + BOARD_HEIGHT / 2) / GRID_SIZE);\n\n            if (this.isValidCoordinate(x, y)) {\n                return {x: x, y: y};\n            }\n            return null;\n        }\n\n        // 对于其他地图大小，使用通用计算方式\n        const { gridSize, boardSize } = this.currentMapConfig;\n        const boardWidth = boardSize.width;\n        const boardHeight = boardSize.height;\n        const gridWidth = gridSize.width;\n        const gridHeight = gridSize.height;\n\n        let x = Math.floor((pos.x + boardWidth / 2) / gridWidth);\n        let y = Math.floor((pos.y + boardHeight / 2) / gridHeight);\n\n        if (this.isValidCoordinate(x, y)) {\n            return {x: x, y: y};\n        }\n        return null;\n    }\n\n    /**\n     * 计算格子的世界坐标位置（完全仿照联机模式）\n     */\n    private getGridWorldPosition(x: number, y: number): cc.Vec2 {\n        if (!this.currentMapConfig) return cc.v2(0, 0);\n\n        const { gridSize, boardSize } = this.currentMapConfig;\n        const boardWidth = boardSize.width;\n        const boardHeight = boardSize.height;\n        const gridWidth = gridSize.width;\n        const gridHeight = gridSize.height;\n\n        // 计算格子中心点位置\n        // 左下角为(0,0)，所以y坐标需要从下往上计算\n        let posX = (x * gridWidth) + (gridWidth / 2) - (boardWidth / 2);\n        let posY = (y * gridHeight) + (gridHeight / 2) - (boardHeight / 2);\n\n        return cc.v2(posX, posY);\n    }\n\n    /**\n     * 为单个格子节点设置触摸事件\n     */\n    private setupSingleGridTouchEvents(gridNode: cc.Node, x: number, y: number) {\n       \n\n        // 长按相关变量\n        let isLongPressing = false;\n        let longPressTimer = 0;\n        let longPressCallback: Function = null;\n        const LONG_PRESS_TIME = 1.0; // 1秒长按时间\n\n        // 触摸开始事件\n        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {\n            isLongPressing = true;\n            longPressTimer = 0;\n\n            // 开始长按检测\n            longPressCallback = () => {\n                if (isLongPressing) {\n                    longPressTimer += 0.1;\n                    if (longPressTimer >= LONG_PRESS_TIME) {\n                        this.onGridLongPress(x, y);\n                        isLongPressing = false;\n                        if (longPressCallback) {\n                            this.unschedule(longPressCallback);\n                        }\n                    }\n                }\n            };\n            this.schedule(longPressCallback, 0.1);\n        }, this);\n\n        // 触摸结束事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n            // 如果不是长按，则执行点击事件\n            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {\n                this.onGridClick(x, y, event);\n            }\n            \n            // 清理长按检测\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 触摸取消事件\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {\n            // 清理长按检测\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 添加Button组件以确保触摸响应（完全仿照联机模式）\n        let button = gridNode.getComponent(cc.Button);\n        if (!button) {\n          \n            button = gridNode.addComponent(cc.Button);\n            button.transition = cc.Button.Transition.SCALE;\n            button.zoomScale = 0.95;\n        } else {\n        \n        }\n\n       \n    }\n\n    /**\n     * 格子点击事件 - 发送挖掘操作\n     */\n    private onGridClick(x: number, y: number, _event?: cc.Event.EventTouch) {\n     \n        \n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`❌ 坐标无效: (${x}, ${y})`);\n            return;\n        }\n\n        // 检查该位置是否已经被揭开或标记\n        const gridData = this.gridData[x][y];\n        if (gridData.isRevealed || gridData.hasPlayer) {\n            console.warn(`⚠️ 格子已被操作: (${x}, ${y}), isRevealed: ${gridData.isRevealed}, hasPlayer: ${gridData.hasPlayer}`);\n            return;\n        }\n\n        \n        // 发送挖掘操作到后端\n        this.sendLevelClickBlock(x, y, 1);\n    }\n\n    /**\n     * 格子长按事件 - 发送标记操作（完全仿照联机模式）\n     */\n    private onGridLongPress(x: number, y: number) {\n  \n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            console.warn(`❌ 坐标无效: (${x}, ${y})`);\n            return;\n        }\n\n        // 检查该位置是否已经有玩家预制体\n        const gridData = this.gridData[x][y];\n        if (gridData.hasPlayer) {\n            console.warn(`⚠️ 格子已被操作，无法标记: (${x}, ${y})`);\n            return;\n        }\n\n       \n        // 发送标记操作到后端（完全仿照联机模式）\n        this.sendLevelClickBlock(x, y, 2);\n    }\n\n    /**\n     * 检查坐标是否有效\n     */\n    private isValidCoordinate(x: number, y: number): boolean {\n        if (!this.currentMapConfig) return false;\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n        return x >= 0 && x < cols && y >= 0 && y < rows;\n    }\n\n    /**\n     * 发送关卡点击方块消息\n     */\n    private sendLevelClickBlock(x: number, y: number, action: number) {\n        const clickData: LevelClickBlockRequest = {\n            x: x,\n            y: y,\n            action: action // 1=挖掘方块，2=标记/取消标记地雷\n        };\n\n       \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeLevelClickBlock, clickData);\n    }\n\n    /**\n     * 处理后端消息\n     */\n    private onReceiveMessage(data: any) {\n        if (data.msgId === MessageId.MsgTypeLevelClickBlock) {\n            this.handleLevelClickBlockResponse(data.data);\n        } else if (data.msgId === MessageId.MsgTypeLevelGameEnd) {\n            this.handleLevelGameEnd(data.data);\n        } else if (data.msgId === MessageId.MsgTypeLeaveRoom) {\n            this.handleLeaveRoom(data.data);\n        } else if (data.msgId === MessageId.MsgTypeExtendLevelInfo) {\n            this.handleExtendLevelInfo(data.data);\n        }\n    }\n\n    /**\n     * 处理关卡点击方块响应（完全仿照联机模式）\n     */\n    private handleLevelClickBlockResponse(responseData: any) {\n  \n\n        const { action, floodFillResults, gameStatus, message, remainingMines, success, x, y, result } = responseData;\n\n        if (!success) {\n            console.error(\"操作失败:\", message);\n            return;\n        }\n\n    \n\n        if (action === 1) {\n            // 挖掘操作\n\n            // 首先处理点击的格子（如果有单独的x,y,result字段）\n            if (x !== undefined && y !== undefined && result !== undefined) {\n             \n                this.playGridDisappearAnimation(x, y, result);\n            }\n\n            // 然后处理连锁展开结果\n            if (floodFillResults && Array.isArray(floodFillResults)) {\n              \n\n                // 仿照联机模式的processFloodFillResult逻辑\n                floodFillResults.forEach((cellResult: any, index: number) => {\n                   \n                    // 尝试不同的字段名，因为后端可能使用不同的字段\n                    const cellX = cellResult.x;\n                    const cellY = cellResult.y;\n                    const cellValue = cellResult.value !== undefined ? cellResult.value :\n                                     cellResult.neighborMines !== undefined ? cellResult.neighborMines :\n                                     cellResult.result !== undefined ? cellResult.result : undefined;\n\n                    if (cellX === undefined || cellY === undefined) {\n                        console.warn(`❌ 格子坐标缺失:`, cellResult);\n                        return;\n                    }\n\n                    if (cellValue === undefined) {\n                        console.warn(`❌ 格子值缺失:`, cellResult);\n                        return;\n                    }\n\n                    if (!this.isValidCoordinate(cellX, cellY)) {\n                        console.warn(`❌ 无效坐标: (${cellX}, ${cellY})`);\n                        return;\n                    }\n\n                    // 延迟播放动画，创造连锁效果（仿照联机模式）\n                    this.scheduleOnce(() => {\n                        this.playGridDisappearAnimation(cellX, cellY, cellValue);\n                    }, index * 0.1); // 每个格子间隔0.1秒\n                });\n            }\n        } else if (action === 2) {\n            // 标记操作 - 处理单个格子标记\n            if (this.isValidCoordinate(x, y)) {\n                const gridData = this.gridData[x][y];\n                gridData.hasPlayer = true;\n                this.createBiaojiPrefab(x, y);\n            }\n        }\n\n        // 检查游戏状态\n        if (gameStatus === 1) {\n           \n        } else if (gameStatus === 3) {\n        \n        }\n    }\n\n    /**\n     * 处理关卡游戏结束通知（完全仿照联机模式）\n     */\n    private handleLevelGameEnd(data: any) {\n       \n\n        const { levelId, isSuccess, gameTime, message, nextLevelId } = data;\n\n        // 显示所有隐藏的格子（完全仿照联机模式）\n        this.showAllHiddenGrids();\n\n        // 显示游戏结束信息\n        if (isSuccess) {\n        \n            \n            if (nextLevelId) {\n                \n            }\n        } else {\n            \n        }\n    }\n\n    /**\n     * 处理ExtendLevelInfo消息（显示所有方块并清理预制体）\n     */\n    private handleExtendLevelInfo(data: any) {\n     \n\n        // 清除所有预制体\n        this.clearAllPrefabs();\n\n        // 显示所有隐藏的格子\n        this.showAllHiddenGrids();\n\n        // 重置游戏状态\n        this.resetGameScene();\n\n\n    }\n\n    /**\n     * 处理离开房间消息（完全仿照联机模式）\n     */\n    private handleLeaveRoom(data: any) {\n    \n        // 显示所有隐藏的格子（完全仿照联机模式）\n        this.showAllHiddenGrids();\n\n        // 隐藏当前页面，打开匹配界面（完全仿照联机模式的退出逻辑）\n        this.exitToMatchingInterface();\n    }\n\n\n\n    /**\n     * 显示所有隐藏的格子（完全仿照联机模式的showAllGrids方法）\n     */\n    private showAllHiddenGrids() {\n        if (!this.currentBoardNode) {\n            console.warn(\"棋盘节点未设置，无法显示格子\");\n            return;\n        }\n\n        let totalGrids = 0;\n        let restoredGrids = 0;\n        let alreadyVisibleGrids = 0;\n\n        console.log(\"🔄 开始显示所有隐藏的格子...\");\n\n        // 遍历棋盘的所有子节点，找到小格子并显示（完全仿照联机模式）\n        for (let i = 0; i < this.currentBoardNode.children.length; i++) {\n            const child = this.currentBoardNode.children[i];\n\n            // 如果是小格子节点（完全仿照联机模式的判断条件）\n            if (child.name.startsWith(\"Grid_\") || child.name === \"block\") {\n                totalGrids++;\n\n                // 记录恢复前的状态（完全仿照联机模式）\n                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;\n\n                if (wasHidden) {\n                    console.log(`恢复格子: ${child.name}`);\n                    restoredGrids++;\n                } else {\n                    alreadyVisibleGrids++;\n                }\n\n                // 停止所有可能正在进行的动画（完全仿照联机模式）\n                child.stopAllActions();\n\n                // 恢复显示状态（完全仿照联机模式）\n                child.active = true;\n                child.opacity = 255;\n                child.scaleX = 1;\n                child.scaleY = 1;\n\n                // 确保格子可以交互（完全仿照联机模式）\n                const button = child.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = true;\n                }\n            }\n        }\n\n        console.log(`✅ 格子恢复完成: 总计${totalGrids}个, 恢复${restoredGrids}个, 已显示${alreadyVisibleGrids}个`);\n\n        // 重置格子数据状态\n        this.resetGridDataStates();\n    }\n\n    /**\n     * 重置格子数据状态\n     */\n    private resetGridDataStates() {\n        if (!this.currentMapConfig) {\n            return;\n        }\n\n        const { rows, cols } = this.currentMapConfig.boardDimensions;\n\n        for (let x = 0; x < cols; x++) {\n            for (let y = 0; y < rows; y++) {\n                if (this.gridData[x] && this.gridData[x][y]) {\n                    this.gridData[x][y].isRevealed = false;\n                    this.gridData[x][y].hasPlayer = false;\n                }\n            }\n        }\n\n        console.log(\"✅ 格子数据状态已重置\");\n    }\n\n    /**\n     * 退出到匹配界面（完全仿照联机模式的退出逻辑）\n     */\n    private exitToMatchingInterface() {\n     \n\n        // 清空所有预制体\n        this.clearAllPrefabs();\n\n        // 显示所有格子\n        this.showAllHiddenGrids();\n\n        // 重置游戏状态\n        this.resetGameScene();\n\n        // 通过事件系统跳转到hall_page（完全仿照联机模式的退出逻辑）\n        console.log(\"🚪 通过事件系统跳转到hall_page...\");\n\n        // 清空数据（仿照GlobalManagerController中的LeaveRoom处理）\n        // GlobalBean.GetInstance().cleanData(); // 如果需要的话可以取消注释\n\n        // 发送跳转到大厅页面的自动消息（完全仿照联机模式）\n        const autoMessageBean = {\n            'msgId': AutoMessageId.JumpHallPage,\n            'data': { 'type': 2 } // 2 是玩家主动离开游戏房间\n        };\n\n        // 通过事件系统发送消息\n        GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);\n\n        console.log(\"✅ 已发送跳转到hall_page的消息\");\n    }\n\n    /**\n     * 播放格子消失动画（完全仿照联机模式）\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     * @param value 格子值：-1=炸弹，0=空格子，1-8=数字\n     */\n    private playGridDisappearAnimation(x: number, y: number, value: any) {\n       \n\n        // 检查坐标是否有效\n        if (!this.isValidCoordinate(x, y)) {\n            console.error(`❌ 坐标无效: (${x}, ${y})`);\n            return;\n        }\n\n        // 检查值是否有效\n        if (value === undefined || value === null) {\n            console.error(`❌ 格子值无效: (${x}, ${y}), 值: ${value}`);\n            return;\n        }\n\n        // 更新格子状态\n        const gridData = this.gridData[x][y];\n        gridData.isRevealed = true;\n        gridData.hasPlayer = true;\n\n        // 先删除格子（仿照联机模式）\n        this.removeGridAt(x, y);\n\n        // 延迟0.3秒后显示结果（等格子消失动画完成）\n        this.scheduleOnce(() => {\n            \n\n            // 转换为数字类型\n            const numValue = typeof value === 'string' ? parseInt(value) : Number(value);\n\n            if (value === \"mine\" || numValue === -1) {\n                // 显示炸弹\n                this.createBoomPrefab(x, y);\n            } else if (numValue >= 1 && numValue <= 8) {\n                // 显示数字\n                this.createNumberPrefab(x, y, numValue);\n            } else if (numValue === 0) {\n               \n            } else {\n                console.warn(`⚠️ 未知的格子值: ${value} (转换后: ${numValue}) at (${x}, ${y})`);\n            }\n        }, 0.3);\n    }\n\n    /**\n     * 隐藏指定位置的格子\n     */\n    private removeGridAt(x: number, y: number) {\n        \n\n        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n        if (gridNode) {\n           \n            // 播放消失动画\n            cc.tween(gridNode)\n                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })\n                .call(() => {\n                    gridNode.active = false;\n                  \n                })\n                .start();\n        } else {\n            console.error(`❌ 未找到格子节点: (${x}, ${y})`);\n            \n            if (this.gridNodes[x]) {\n                \n            }\n        }\n    }\n\n    /**\n     * 计算预制体位置（根据不同地图大小）\n     */\n    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {\n        if (!this.currentMapConfig) {\n            console.error(\"当前地图配置未设置\");\n            return cc.v2(0, 0);\n        }\n\n        // 根据地图类型使用不同的计算公式\n        switch (this.currentMapType) {\n            case \"8x8\":\n                // 8x8和联机模式一样的计算方式\n                return this.calculate8x8Position(x, y);\n            case \"8x9\":\n                // 8x9棋盘大小752*845 格子大小88*88\n                return this.calculate8x9Position(x, y);\n            case \"9x9\":\n                // 9x9棋盘大小752*747 格子大小76*76\n                return this.calculate9x9Position(x, y);\n            case \"9x10\":\n                // 9x10棋盘大小752*830 格子大小78*78\n                return this.calculate9x10Position(x, y);\n            case \"10x10\":\n                // 10x10棋盘大小752*745格子大小69*69\n                return this.calculate10x10Position(x, y);\n            default:\n                console.error(`不支持的地图类型: ${this.currentMapType}`);\n                return cc.v2(0, 0);\n        }\n    }\n\n    /**\n     * 计算8x8地图位置（完全仿照联机模式的固定坐标）\n     */\n    private calculate8x8Position(x: number, y: number): cc.Vec2 {\n        // 完全仿照联机模式ChessBoardController的calculatePrefabPosition方法\n        // (0,0) → (-314, -310)\n        // (1,0) → (-224, -310)  // x增加90\n        // (0,1) → (-314, -222)  // y增加88\n        // (7,7) → (310, 312)\n\n        const startX = -314;  // 起始X坐标\n        const startY = -310;  // 起始Y坐标\n        const stepX = 90;     // X方向步长\n        const stepY = 88;     // Y方向步长\n\n        const finalX = startX + (x * stepX);\n        const finalY = startY + (y * stepY);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 计算8x9地图位置\n     */\n    private calculate8x9Position(x: number, y: number): cc.Vec2 {\n        // 棋盘大小752*845 格子大小88*88\n        const boardWidth = 752;\n        const boardHeight = 845;\n        const gridWidth = 88;\n        const gridHeight = 88;\n\n        // 计算起始位置（棋盘中心对齐）\n        const startX = -(boardWidth / 2) + (gridWidth / 2);\n        const startY = -(boardHeight / 2) + (gridHeight / 2);\n\n        const finalX = startX + (x * gridWidth);\n        const finalY = startY + (y * gridHeight);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 计算9x9地图位置\n     */\n    private calculate9x9Position(x: number, y: number): cc.Vec2 {\n        // 棋盘大小752*747 格子大小76*76\n        const boardWidth = 752;\n        const boardHeight = 747;\n        const gridWidth = 76;\n        const gridHeight = 76;\n\n        const startX = -(boardWidth / 2) + (gridWidth / 2);\n        const startY = -(boardHeight / 2) + (gridHeight / 2);\n\n        const finalX = startX + (x * gridWidth);\n        const finalY = startY + (y * gridHeight);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 计算9x10地图位置\n     */\n    private calculate9x10Position(x: number, y: number): cc.Vec2 {\n        // 棋盘大小752*830 格子大小78*78\n        const boardWidth = 752;\n        const boardHeight = 830;\n        const gridWidth = 78;\n        const gridHeight = 78;\n\n        const startX = -(boardWidth / 2) + (gridWidth / 2);\n        const startY = -(boardHeight / 2) + (gridHeight / 2);\n\n        const finalX = startX + (x * gridWidth);\n        const finalY = startY + (y * gridHeight);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 计算10x10地图位置\n     */\n    private calculate10x10Position(x: number, y: number): cc.Vec2 {\n        // 棋盘大小752*745格子大小69*69\n        const boardWidth = 752;\n        const boardHeight = 745;\n        const gridWidth = 69;\n        const gridHeight = 69;\n\n        const startX = -(boardWidth / 2) + (gridWidth / 2);\n        const startY = -(boardHeight / 2) + (gridHeight / 2);\n\n        const finalX = startX + (x * gridWidth);\n        const finalY = startY + (y * gridHeight);\n\n        return cc.v2(finalX, finalY);\n    }\n\n    /**\n     * 创建炸弹预制体（完全仿照联机模式）\n     */\n    private createBoomPrefab(x: number, y: number) {\n        if (!this.boomPrefab) {\n            console.error(\"boomPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化炸弹预制体\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"Boom\";\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        boomNode.setPosition(position);\n\n        // 直接添加到棋盘节点（完全仿照联机模式）\n        if (this.currentBoardNode) {\n            this.currentBoardNode.addChild(boomNode);\n        }\n\n        // 播放出现动画\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })\n            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })\n            .start();\n\n        \n    }\n\n    /**\n     * 创建标记预制体（完全仿照联机模式）\n     */\n    private createBiaojiPrefab(x: number, y: number) {\n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化标记预制体\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"Biaoji\";\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        biaojiNode.setPosition(position);\n\n        // 直接添加到棋盘节点（完全仿照联机模式）\n        if (this.currentBoardNode) {\n            this.currentBoardNode.addChild(biaojiNode);\n        }\n\n        // 播放出现动画\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n\n    \n    }\n\n    /**\n     * 创建数字预制体\n     */\n    private createNumberPrefab(x: number, y: number, number: number) {\n        // 根据数字选择对应的预制体\n        let prefab: cc.Prefab = null;\n        switch (number) {\n            case 1: prefab = this.boom1Prefab; break;\n            case 2: prefab = this.boom2Prefab; break;\n            case 3: prefab = this.boom3Prefab; break;\n            case 4: prefab = this.boom4Prefab; break;\n            case 5: prefab = this.boom5Prefab; break;\n            case 6: prefab = this.boom6Prefab; break;\n            case 7: prefab = this.boom7Prefab; break;\n            case 8: prefab = this.boom8Prefab; break;\n            default:\n                console.error(`不支持的数字: ${number}`);\n                return;\n        }\n\n        if (!prefab) {\n            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);\n            return;\n        }\n\n        // 实例化数字预制体\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `Boom${number}`;\n\n        // 设置位置\n        const position = this.calculatePrefabPosition(x, y);\n        numberNode.setPosition(position);\n\n        // 直接添加到棋盘节点（完全仿照联机模式）\n        if (this.currentBoardNode) {\n            this.currentBoardNode.addChild(numberNode);\n        }\n\n        // 播放出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n\n       \n    }\n\n    /**\n     * 清空所有预制体（完全仿照联机模式，但保留格子）\n     */\n    public clearAllPrefabs() {\n        if (!this.currentBoardNode) {\n            console.warn(\"棋盘节点不存在，无法清理预制体\");\n            return;\n        }\n\n        const childrenToRemove: cc.Node[] = [];\n        let totalCleared = 0;\n\n        // 遍历棋盘的所有子节点，找到预制体节点\n        for (let i = 0; i < this.currentBoardNode.children.length; i++) {\n            const child = this.currentBoardNode.children[i];\n            const nodeName = child.name;\n\n            // 保留条件：保留Grid_开头的节点（格子）和其他重要节点\n            const shouldKeep = nodeName.startsWith(\"Grid_\") ||\n                             nodeName.includes(\"Background\") ||\n                             nodeName.includes(\"UI\") ||\n                             nodeName.includes(\"Canvas\") ||\n                             nodeName === \"PrefabContainer\"; // 如果有的话也保留\n\n            // 只删除预制体：Boom, Biaoji, Boom1-8等\n            const isPrefab = nodeName === \"Boom\" ||\n                           nodeName === \"Biaoji\" ||\n                           nodeName.startsWith(\"Boom\") ||\n                           nodeName === \"HexBoom\" ||\n                           nodeName === \"HexBiaoji\";\n\n            if (isPrefab && !shouldKeep) {\n                childrenToRemove.push(child);\n                totalCleared++;\n               \n            }\n        }\n\n        // 移除找到的预制体节点\n        childrenToRemove.forEach(child => {\n            child.removeFromParent();\n        });\n\n       \n    }\n\n    /**\n     * 重置游戏场景\n     */\n    public resetGameScene() {\n        // 清空所有预制体\n        this.clearAllPrefabs();\n\n        // 重置格子数据\n        if (this.currentMapConfig) {\n            const { rows, cols } = this.currentMapConfig.boardDimensions;\n            for (let x = 0; x < cols; x++) {\n                for (let y = 0; y < rows; y++) {\n                    if (this.gridData[x] && this.gridData[x][y]) {\n                        this.gridData[x][y].isRevealed = false;\n                        this.gridData[x][y].hasPlayer = false;\n                        this.gridData[x][y].isMine = false;\n                        this.gridData[x][y].number = 0;\n                    }\n\n                    // 恢复格子显示\n                    const gridNode = this.gridNodes[x] && this.gridNodes[x][y];\n                    if (gridNode) {\n                        gridNode.active = true;\n                        gridNode.setScale(1, 1);\n                    }\n                }\n            }\n        }\n\n       \n    }\n\n   \n\n}"]}