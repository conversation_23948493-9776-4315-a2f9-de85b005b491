{"version": 3, "sources": ["assets/scripts/level/LevelPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,8CAA6C;AAC7C,4DAA2D;AAC3D,uEAAkE;AAClE,uCAAsC;AACtC,yCAAwC;AACxC,mEAAkE;AAE5D,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEA2bC;QAzbG,OAAO;QAEP,gBAAU,GAAY,IAAI,CAAC;QAE3B,SAAS;QAET,qBAAe,GAAc,IAAI,CAAC;QAElC,UAAU;QAEV,oBAAc,GAAa,IAAI,CAAC;QAEhC,YAAY;QAEZ,uBAAiB,GAAa,IAAI,CAAC;QAEnC,SAAS;QAET,2BAAqB,GAA0B,IAAI,CAAC;QAEpD,eAAe;QAEf,mBAAa,GAAY,IAAI,CAAC;QAE9B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,WAAW;QAEX,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,oBAAc,GAAY,IAAI,CAAC,CAAC,4CAA4C;QAE5E,WAAW;QAEX,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAE1E,UAAU;QAEV,4BAAsB,GAA2B,IAAI,CAAC;QAEtD,SAAS;QACD,kBAAY,GAAW,CAAC,CAAC;QACzB,sBAAgB,GAA4B,IAAI,CAAC;QACjD,mBAAa,GAAW,CAAC,CAAC,CAAC,cAAc;;IA+WrD,CAAC;IA7WG,oCAAM,GAAN;QAAA,iBAcC;QAbG,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,eAAM,CAAC,SAAS,GAAG,sBAAsB,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE;gBAC3H,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;QAED,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,iDAAiD;IACrD,CAAC;IAED,mCAAK,GAAL;QACI,eAAe;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IAE3B,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QAGI,oCAAoC;QACpC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAE5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;YAEnC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAC1B;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QAEI,+BAA+B;QAC/B,IAAM,OAAO,GAA2B;YACpC,OAAO,EAAE,IAAI,CAAC,YAAY;SAC7B,CAAC;QAGF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;;OAGG;IACI,+CAAiB,GAAxB,UAAyB,SAAkC;QAGvD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,iBAAiB;QACjB,IAAI,SAAS,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC;SAEzC;QAED,UAAU;QACV,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE5C,8BAA8B;QAC9B,4BAA4B;QAE5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,SAAiB;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;SAErD;IACL,CAAC;IAED;;;OAGG;IACK,kDAAoB,GAA5B,UAA6B,WAAmB;QAC5C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,WAAI,WAAW,WAAG,CAAC;SAEtD;IACL,CAAC;IAED;;;OAGG;IACK,wCAAU,GAAlB,UAAmB,WAAmB;QAGlC,YAAY;QACZ,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAIvC,YAAY;QACZ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,iBAAiB;QACjB,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE;YAC1B,iDAAiD;YACjD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,oDAAoD;YACpD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,qDAAqD;YACrD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;SACrD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,sDAAsD;YACtD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SACvD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,sDAAsD;YACtD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SACvD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,iDAAY,WAAa,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;OAIG;IACK,yCAAW,GAAnB,UAAoB,OAAgB,EAAE,OAAe;QACjD,IAAI,OAAO,EAAE;YAET,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YAEtB,iBAAiB;YACjB,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,OAAO,WAAW,EAAE;gBAChB,sBAAsB;gBACtB,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY,IAAI,WAAW,CAAC,IAAI,KAAK,OAAO,EAAE;oBACnE,WAAW,CAAC,IAAI,CAAI,WAAW,CAAC,IAAI,YAAS,CAAC,CAAC;iBAClD;qBAAM;oBACH,WAAW,CAAC,IAAI,CAAI,WAAW,CAAC,IAAI,SAAI,WAAW,CAAC,MAAM,MAAG,CAAC,CAAC;iBAClE;gBACD,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;aACpC;YAED,YAAY;YACZ,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAEtD;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,wDAAc,OAAS,CAAC,CAAC;YACjC,EAAE,CAAC,IAAI,CAAC,iFAAkC,OAAO,8BAAO,CAAC,CAAC;SAC7D;IACL,CAAC;IAED;;OAEG;IACK,6CAAe,GAAvB;QACI,IAAM,WAAW,GAAG;YAChB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;SACrB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;YACpB,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,6CAAe,GAAtB,UAAuB,WAAmB;QAEtC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAGhC,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,iDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,8CAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,kDAAoB,GAA5B;QACI,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SAEpC;QAED,cAAc;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QAED,gBAAgB;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;;;OAIG;IACK,yDAA2B,GAAnC,UAAoC,OAAe,EAAE,SAAkB;QACnE,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,eAAe;YACf,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,KAAK;YAE1B,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACzB,OAAO,GAAG,KAAK,CAAC;aACnB;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAChC,OAAO,GAAG,KAAK,CAAC;aACnB;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAChC,OAAO,GAAG,KAAK,CAAC;aACnB;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACjC,OAAO,GAAG,MAAM,CAAC;aACpB;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAClC,OAAO,GAAG,OAAO,CAAC;aACrB;YAED,wBAAwB;YACxB,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEhD,WAAW;YACX,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,CAAC;SAGjD;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;SAC3C;IACL,CAAC;IAED;;OAEG;IACI,qDAAuB,GAA9B;QAGI,WAAW;QACX,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC;SACrC;QAED,WAAW;QACX,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QAEvB,aAAa;QACb,sBAAsB;QACtB,eAAe;QACf,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAE/B,sCAAsC;QACtC,gBAAgB;IACpB,CAAC;IAtbD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACS;IAI3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAIlC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;+DACa;IAIhC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;kEACgB;IAInC;QADC,QAAQ,CAAC,+BAAqB,CAAC;sEACoB;IAIpD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACa;IAI/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,+CAAsB,CAAC;uEACqB;IAvErC,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CA2bvC;IAAD,0BAAC;CA3bD,AA2bC,CA3bgD,EAAE,CAAC,SAAS,GA2b5D;kBA3boB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { ExtendLevelInfoRequest, ExtendLevelInfoResponse } from \"../bean/GameBean\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport LeaveDialogController from \"../hall/LeaveDialogController\";\nimport { Tools } from \"../util/Tools\";\nimport { Config } from \"../util/Config\";\nimport { SinglePlayerController } from \"./SinglePlayerController\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelPageController extends cc.Component {\n\n    // 返回按钮\n    @property(cc.Node)\n    backButton: cc.Node = null;\n\n    // 开始游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    // 地雷数UI标签\n    @property(cc.Label)\n    mineCountLabel: cc.Label = null;\n\n    // 当前关卡数UI标签\n    @property(cc.Label)\n    currentLevelLabel: cc.Label = null;\n\n    // 退出游戏弹窗\n    @property(LeaveDialogController)\n    leaveDialogController: LeaveDialogController = null;\n\n    // level_page节点\n    @property(cc.Node)\n    levelPageNode: cc.Node = null;\n\n    // game_map_1节点\n    @property(cc.Node)\n    gameMap1Node: cc.Node = null;\n\n    // game_map_2节点\n    @property(cc.Node)\n    gameMap2Node: cc.Node = null;\n\n    // 方形地图节点引用\n    @property(cc.Node)\n    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8\n\n    @property(cc.Node)\n    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9\n\n    @property(cc.Node)\n    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9\n\n    @property(cc.Node)\n    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10\n\n    @property(cc.Node)\n    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10\n\n    // 特殊关卡节点引用\n    @property(cc.Node)\n    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001\n\n    @property(cc.Node)\n    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002\n\n    @property(cc.Node)\n    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003\n\n    @property(cc.Node)\n    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004\n\n    @property(cc.Node)\n    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005\n\n    @property(cc.Node)\n    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006\n\n    // 单机模式控制器\n    @property(SinglePlayerController)\n    singlePlayerController: SinglePlayerController = null;\n\n    // 当前关卡数据\n    private currentLevel: number = 1;\n    private currentLevelInfo: ExtendLevelInfoResponse = null;\n    private currentRoomId: number = 0; // 当前关卡游戏的房间ID\n\n    onLoad() {\n        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式\n        if (this.backButton) {\n            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {\n                this.onBackButtonClick();\n            });\n        }\n\n        // 设置开始游戏按钮点击事件\n        if (this.startGameButton) {\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n\n        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新\n    }\n\n    start() {\n        // 初始化时隐藏所有地图节点\n        this.hideAllMapNodes();\n\n    }\n\n    /**\n     * 返回按钮点击事件\n     */\n    private onBackButtonClick() {\n      \n\n        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID\n        if (this.leaveDialogController) {\n           \n            this.leaveDialogController.show(1, () => {\n               \n            }, this.currentRoomId);\n        } else {\n            cc.warn(\"LeaveDialogController 未配置\");\n        }\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n       \n        // 发送ExtendLevelInfo消息到后端获取地图数据\n        const request: ExtendLevelInfoRequest = {\n            levelId: this.currentLevel\n        };\n\n      \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);\n    }\n\n    /**\n     * 处理ExtendLevelInfo响应\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {\n    \n\n        this.currentLevelInfo = levelInfo;\n\n        // 保存房间ID，用于退出时使用\n        if (levelInfo.roomId) {\n            this.currentRoomId = levelInfo.roomId;\n           \n        }\n\n        // 更新地雷数UI\n        this.updateMineCountUI(levelInfo.mineCount);\n\n        // 使用当前设置的关卡编号，而不是后端返回的levelId\n        // 因为后端的levelId可能与前端的关卡编号不一致\n       \n        this.enterLevel(this.currentLevel);\n    }\n\n    /**\n     * 更新地雷数UI\n     * @param mineCount 地雷数量\n     */\n    private updateMineCountUI(mineCount: number) {\n        if (this.mineCountLabel) {\n            this.mineCountLabel.string = mineCount.toString();\n           \n        }\n    }\n\n    /**\n     * 更新当前关卡数UI\n     * @param levelNumber 关卡编号\n     */\n    private updateCurrentLevelUI(levelNumber: number) {\n        if (this.currentLevelLabel) {\n            this.currentLevelLabel.string = `第${levelNumber}关`;\n           \n        }\n    }\n\n    /**\n     * 根据关卡数进入相应的关卡\n     * @param levelNumber 关卡编号\n     */\n    private enterLevel(levelNumber: number) {\n    \n\n        // 更新关卡数UI显示\n        this.updateCurrentLevelUI(levelNumber);\n\n     \n\n        // 先隐藏所有地图容器\n        this.hideAllMapContainers();\n\n        // 根据关卡数显示对应的地图节点\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8\n            this.showGameMap1();\n            this.showMapNode(this.qipan8x8Node, \"qipan8*8\");\n        } else if (levelNumber === 5) {\n            // 第5关，打开level_page/game_map_2/game_bg/Level_S001\n            this.showGameMap2();\n            this.showMapNode(this.levelS001Node, \"Level_S001\");\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9\n            this.showGameMap1();\n            this.showMapNode(this.qipan8x9Node, \"qipan8*9\");\n        } else if (levelNumber === 10) {\n            // 第10关，打开level_page/game_map_2/game_bg/Level_S002\n            this.showGameMap2();\n            this.showMapNode(this.levelS002Node, \"Level_S002\");\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9\n            this.showGameMap1();\n            this.showMapNode(this.qipan9x9Node, \"qipan9*9\");\n        } else if (levelNumber === 15) {\n            // 第15关，打开level_page/game_map_2/game_bg/Level_S003\n            this.showGameMap2();\n            this.showMapNode(this.levelS003Node, \"Level_S003\");\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10\n            this.showGameMap1();\n            this.showMapNode(this.qipan9x10Node, \"qipan9*10\");\n        } else if (levelNumber === 20) {\n            // 第20关，打开level_page/game_map_2/game_bg/Level_S004\n            this.showGameMap2();\n            this.showMapNode(this.levelS004Node, \"Level_S004\");\n        } else if (levelNumber >= 21 && levelNumber <= 24) {\n            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10\n            this.showGameMap1();\n            this.showMapNode(this.qipan10x10Node, \"qipan10*10\");\n        } else if (levelNumber === 25) {\n            // 第25关，打开level_page/game_map_2/game_bg/Level_S005\n            this.showGameMap2();\n            this.showMapNode(this.levelS005Node, \"Level_S005\");\n        } else if (levelNumber >= 26 && levelNumber <= 29) {\n            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10\n            this.showGameMap1();\n            this.showMapNode(this.qipan10x10Node, \"qipan10*10\");\n        } else if (levelNumber === 30) {\n            // 第30关，打开level_page/game_map_2/game_bg/Level_S006\n            this.showGameMap2();\n            this.showMapNode(this.levelS006Node, \"Level_S006\");\n        } else {\n            cc.warn(`未知的关卡编号: ${levelNumber}`);\n        }\n    }\n\n    /**\n     * 显示指定的地图节点\n     * @param mapNode 要显示的地图节点\n     * @param mapName 地图名称（用于日志）\n     */\n    private showMapNode(mapNode: cc.Node, mapName: string) {\n        if (mapNode) {\n\n            mapNode.active = true;\n\n            // 检查父节点链是否都是激活状态\n            let currentNode = mapNode.parent;\n            let parentChain = [];\n            while (currentNode) {\n                // 避免访问场景节点的 active 属性\n                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {\n                    parentChain.push(`${currentNode.name}(Scene)`);\n                } else {\n                    parentChain.push(`${currentNode.name}(${currentNode.active})`);\n                }\n                currentNode = currentNode.parent;\n            }\n\n            // 设置单机模式控制器\n            this.setupSinglePlayerController(mapName, mapNode);\n\n        } else {\n            cc.warn(`❌ 地图节点未找到: ${mapName}`);\n            cc.warn(`请在编辑器中为 LevelPageController 配置 ${mapName} 节点属性`);\n        }\n    }\n\n    /**\n     * 隐藏所有地图节点\n     */\n    private hideAllMapNodes() {\n        const allMapNodes = [\n            this.qipan8x8Node,\n            this.qipan8x9Node,\n            this.qipan9x9Node,\n            this.qipan9x10Node,\n            this.qipan10x10Node,\n            this.levelS001Node,\n            this.levelS002Node,\n            this.levelS003Node,\n            this.levelS004Node,\n            this.levelS005Node,\n            this.levelS006Node\n        ];\n\n        allMapNodes.forEach(node => {\n            if (node) {\n                node.active = false;\n            }\n        });\n    }\n\n    /**\n     * 设置当前关卡（从外部调用）\n     * @param levelNumber 关卡编号\n     */\n    public setCurrentLevel(levelNumber: number) {\n      \n        this.currentLevel = levelNumber;\n\n\n        // 立即根据关卡数切换地图显示\n        this.enterLevel(levelNumber);\n    }\n\n    /**\n     * 获取当前关卡编号\n     */\n    public getCurrentLevel(): number {\n        return this.currentLevel;\n    }\n\n    /**\n     * 获取当前关卡信息\n     */\n    public getCurrentLevelInfo(): ExtendLevelInfoResponse {\n        return this.currentLevelInfo;\n    }\n\n    /**\n     * 获取当前房间ID\n     */\n    public getCurrentRoomId(): number {\n        return this.currentRoomId;\n    }\n\n    /**\n     * 隐藏所有地图容器\n     */\n    private hideAllMapContainers() {\n        // 确保 level_page 节点是激活的\n        if (this.levelPageNode) {\n            this.levelPageNode.active = true;\n          \n        }\n\n        // 隐藏两个主要的地图容器\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = false;\n            \n        }\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = false;\n           \n        }\n\n        // 同时隐藏所有具体的地图节点\n        this.hideAllMapNodes();\n    }\n\n    /**\n     * 显示 game_map_1 容器（方形地图）\n     */\n    private showGameMap1() {\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = true;\n            \n        } else {\n            cc.warn(\"❌ game_map_1 节点未找到\");\n        }\n    }\n\n    /**\n     * 显示 game_map_2 容器（特殊关卡）\n     */\n    private showGameMap2() {\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = true;\n\n        } else {\n            cc.warn(\"❌ game_map_2 节点未找到\");\n        }\n    }\n\n    /**\n     * 设置单机模式控制器\n     * @param mapName 地图名称\n     * @param boardNode 棋盘节点\n     */\n    private setupSinglePlayerController(mapName: string, boardNode: cc.Node) {\n        if (this.singlePlayerController) {\n            // 根据地图名称确定地图类型\n            let mapType = \"8x8\"; // 默认\n\n            if (mapName.includes(\"8*8\")) {\n                mapType = \"8x8\";\n            } else if (mapName.includes(\"8*9\")) {\n                mapType = \"8x9\";\n            } else if (mapName.includes(\"9*9\")) {\n                mapType = \"9x9\";\n            } else if (mapName.includes(\"9*10\")) {\n                mapType = \"9x10\";\n            } else if (mapName.includes(\"10*10\")) {\n                mapType = \"10x10\";\n            }\n\n            // 设置地图类型（这会自动设置对应的棋盘节点）\n            this.singlePlayerController.setMapType(mapType);\n\n            // 清空之前的预制体\n            this.singlePlayerController.clearAllPrefabs();\n\n           \n        } else {\n            cc.warn(\"❌ SinglePlayerController 未配置\");\n        }\n    }\n\n    /**\n     * 退出到匹配界面（由SinglePlayerController调用）\n     */\n    public exitToMatchingInterface() {\n       \n\n        // 隐藏当前关卡页面\n        if (this.levelPageNode) {\n            this.levelPageNode.active = false;\n        }\n\n        // 清空当前关卡数据\n        this.currentLevel = 1;\n        this.currentLevelInfo = null;\n        this.currentRoomId = 0;\n\n        // 这里应该打开匹配界面\n        // 可以通过事件系统通知全局管理器切换页面\n        // 或者直接调用场景切换逻辑\n        console.log(\"📱 应该切换到匹配/大厅界面\");\n\n        // 可以参考GlobalManagerController中的页面切换逻辑\n        // 或者发送自动消息来切换页面\n    }\n}\n"]}