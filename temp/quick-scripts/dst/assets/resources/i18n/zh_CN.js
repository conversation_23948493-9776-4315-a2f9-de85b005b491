
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_CN.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '83ee5xvZ1VBLb3UezEVIqou', 'zh_CN');
// resources/i18n/zh_CN.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: '您被请出房间',
    LeaveRoom: '房间已解散',
    InsufficientBalance: '余额不足，去充值',
    GameRouteNotFound: '游戏线路异常',
    NetworkError: '网络异常',
    RoomIsFull: '房间已满',
    EnterRoomNumber: '输入房间号',
    GetUserInfoFailed: '获取用户信息失败',
    RoomDoesNotExist: '房间不存在',
    FailedToDeductGoldCoins: '扣除金币失败',
    ExitApplication: '确定退出游戏？',
    QuitTheGame: '退出后将无法返回游戏。',
    NotEnoughPlayers: '玩家数量不足',
    TheGameIsFullOfPlayers: '玩家数量已满',
    kickout2: '是否将 {0} 请出房间?',
    upSeat: '加入游戏',
    downSeat: '退出游戏',
    startGame: '开始',
    readyGame: '准备',
    cancelGame: '取消准备',
    cancel: '取消',
    confirm: '确定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音乐',
    sound: '音效',
    join: '加入',
    create: '创建',
    auto: '匹配',
    Room: '房间',
    room_number: '房间号',
    copy: '复制',
    game_amount: '游戏费用',
    player_numbers: '玩家数量:',
    room_exist: '房间不存在',
    enter_room_number: '输入房间号',
    free: '免费',
    players: '玩家',
    Player: '玩家',
    Tickets: '门票',
    Empty: '空位',
    nextlevel: '下一关',
    relevel: '再玩一次',
    danjiguize: "单机规则",
    lianjuguize: "联机规则",
    dtips1: "游戏简介：",
    dtips2: "安全区：",
    dtips3: "雷区：",
    dtips4: "游戏目标：",
    dtips5: "标记：",
    dtips6: "提示：",
    dinfo1: "游戏中存在一些格子，翻开后分为数字格子，空白格子和地雷，玩家可以通过点击翻开新格子，数字格子可以同帮助玩家获得周围相邻格子中存在的地雷数量，若翻到空白格子会继续扩散翻牌，直到翻到数字格子才停止，利用游戏规则，顺利通关吧。",
    dinfo2: "数字格子与空白格子统称为安全区",
    dinfo3: "翻到雷区，会导致游戏失败。",
    dinfo4: "在不触发任何雷区的情况下，揭示游戏区域中的所有安全格子。",
    dinfo5: "玩家可以通过对格子长按，插旗子来标记雷区，标记不会翻开该格子。",
    dinfo6: "玩家每局可获得一次提示，仅本局生效，点击提示可以帮助玩家显示出一个安全的格子。一局最多使用4次提示。",
    ltips1: "游戏简介：",
    ltips2: "游戏人数：",
    ltips3: "回合时间：",
    ltips4: "游戏目标：",
    ltips5: "标记：",
    ltips6: "得分规则：",
    linfo1: "与单机模式相同，格子的样式一致。每个回合所有玩家同时选择任意一个格子，时间结束后或所有人全部选择完毕后，展示所有人的选择情况并根据格子的内容进行加减分，待所有格子都被选完，游戏结束。争取得到更多的分数吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用游戏规则，进行得分，分数最高者获得胜利。",
    linfo5: "长按可对格子进行雷区标记，若该格子为雷区，则可额外获得加分。",
    linfo6: "1. 每回合最先选择格子的玩家获得1分。\n2. 单击翻开格子，若为安全格子则加6分，为雷区则扣12分。\n3. 长按进行标记翻开格子，若为雷区则加10分，为安全区则不加分。\n4. 多人同时选择一个格子，根据选择的对错结果，进行平均加分，例如两人单击选择同一个格子，该格子为安全区，则每人得3分。",
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_CN = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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