
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/resources/i18n/zh_HK.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'df865DJwGFNyZgxO3JjZjqY', 'zh_HK');
// resources/i18n/zh_HK.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //這部分是通用的
    kickout1: '您被請出房間',
    LeaveRoom: '房間已解散',
    InsufficientBalance: '餘額不足，去儲值',
    GameRouteNotFound: '遊戲路線異常',
    NetworkError: '網絡異常',
    RoomIsFull: '房間已滿',
    EnterRoomNumber: '輸入房間號',
    GetUserInfoFailed: '獲取用戶資訊失敗',
    RoomDoesNotExist: '房間不存在',
    FailedToDeductGoldCoins: '扣除金幣失敗',
    ExitApplication: '確定退出遊戲？',
    QuitTheGame: '退出後將無法返回遊戲。',
    NotEnoughPlayers: '玩家數量不足',
    TheGameIsFullOfPlayers: '玩家數量已滿',
    kickout2: '是否將 {0} 請出房間?',
    upSeat: '加入遊戲',
    downSeat: '退出遊戲',
    startGame: '開始',
    readyGame: '準備',
    cancelGame: '取消準備',
    cancel: '取消',
    confirm: '確定',
    kickout3: '踢出',
    back: '返回',
    leave: '退出',
    music: '音樂',
    sound: '音效',
    join: '進入',
    create: '創建',
    auto: '匹配',
    Room: '房間',
    room_number: '房號',
    copy: '複製',
    game_amount: '遊戲費用',
    player_numbers: '玩家數量:',
    room_exist: '房間不存在',
    enter_room_number: '輸入房間號',
    free: '免費',
    players: '玩家',
    Player: '玩家',
    Tickets: '門票',
    Empty: '空位',
    nextlevel: '下一關',
    relevel: '再玩一次',
    danjiguize: "單機規則",
    lianjuguize: "聯機規則",
    dtips1: "遊戲簡介：",
    dtips2: "安全區：",
    dtips3: "雷區：",
    dtips4: "遊戲目標：",
    dtips5: "標記：",
    dtips6: "提示：",
    dinfo1: "遊戲中存在一些格子，翻開後分為數字格子、空白格子和地雷。玩家可以通過點擊翻開新格子，數字格子可以幫助玩家獲得周圍相鄰格子中存在的地雷數量。若翻到空白格子會繼續擴散翻牌，直到翻到數字格子才停止。利用遊戲規則，順利通關吧。",
    dinfo2: "數字格子與空白格子統稱為安全區。",
    dinfo3: "翻到雷區，會導致遊戲失敗。",
    dinfo4: "在不觸發任何雷區的情況下，揭示遊戲區域中的所有安全格子。",
    dinfo5: "玩家可以通過對格子長按，插旗子來標記雷區，標記不會翻開該格子。",
    dinfo6: "玩家每局可獲得一次提示，僅本局生效。點擊提示可以幫助玩家顯示出一個安全的格子。一局最多使用4次提示。",
    ltips1: "遊戲簡介：",
    ltips2: "遊戲人數：",
    ltips3: "回合時間：",
    ltips4: "遊戲目標：",
    ltips5: "標記：",
    ltips6: "得分規則：",
    linfo1: "與單機模式相同，格子的樣式一致。每個回合所有玩家同時選擇任意一個格子，時間結束後或所有人全部選擇完畢後，展示所有人的選擇情況並根據格子的內容進行加減分。待所有格子都被選完，遊戲結束。爭取得到更多的分數吧！",
    linfo2: "2人/3人/4人",
    linfo3: "20秒",
    linfo4: "利用遊戲規則進行得分，分數最高者獲得勝利。",
    linfo5: "長按可對格子進行雷區標記，若該格子為雷區，則可額外獲得加分。",
    linfo6: "1. 每回合最先選擇格子的玩家獲得1分。\n2. 單擊翻開格子，若為安全格子則加6分，為雷區則扣12分。\n3. 長按進行標記翻開格子，若為雷區則加10分，為安全區則不加分。\n4. 多人同時選擇一個格子，根據選擇的對錯結果進行平均加分。例如兩人單擊選擇同一個格子，該格子為安全區，則每人得3分。"
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.zh_HK = exports.language;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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