
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'eed91BOnAtLKYkInYtuL76o', 'LevelSelectPageController');
// scripts/hall/Level/LevelSelectPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalManagerController_1 = require("../../GlobalManagerController");
var LevelSelectController_1 = require("./LevelSelectController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelSelectPageController = /** @class */ (function (_super) {
    __extends(LevelSelectPageController, _super);
    function LevelSelectPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.levelSelectController = null;
        _this.scrollView = null;
        // 新增的游戏按钮
        _this.startGameButton = null;
        _this.lockedButton = null;
        return _this;
    }
    LevelSelectPageController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        // 设置按钮初始状态 - 默认显示开始游戏按钮，避免状态切换闪烁
        if (this.startGameButton) {
            this.startGameButton.node.active = true;
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = false;
            this.lockedButton.node.on('click', this.onLockedButtonClick, this);
        }
    };
    LevelSelectPageController.prototype.start = function () {
        var _this = this;
        // 设置关卡选择变化回调
        if (this.levelSelectController) {
            this.levelSelectController.onLevelSelectionChanged = function (levelNumber) {
                _this.onLevelSelectionChanged(levelNumber);
            };
        }
        // 延迟更新UI，确保在关卡数据加载后再更新按钮状态
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.1);
    };
    /**
     * 更新UI显示状态
     */
    LevelSelectPageController.prototype.updateUIDisplay = function () {
        // 更新游戏按钮状态
        this.updateGameButtons();
    };
    /**
     * 进入选中的关卡
     */
    LevelSelectPageController.prototype.enterSelectedLevel = function () {
        if (this.levelSelectController) {
            var selectedLevel_1 = this.levelSelectController.getCurrentSelectedLevel();
            // 尝试多种方式获取全局管理器
            var globalManager_1 = null;
            // 方法1: 尝试查找 global_node 节点（根目录）
            var globalManagerNode = cc.find("global_node");
            if (globalManagerNode) {
                globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
            }
            // 方法1.1: 尝试查找 Canvas/global_node 节点
            if (!globalManager_1) {
                globalManagerNode = cc.find("Canvas/global_node");
                if (globalManagerNode) {
                    globalManager_1 = globalManagerNode.getComponent(GlobalManagerController_1.default);
                    if (globalManager_1) {
                    }
                    else {
                        // 列出节点上的所有组件
                        var components = globalManagerNode.getComponents(cc.Component);
                    }
                }
            }
            if (globalManager_1) {
                // 先切换到关卡页面
                globalManager_1.setCurrentPage(GlobalManagerController_1.PageType.LEVEL_PAGE);
                // 延迟设置关卡，确保页面切换完成后再设置
                this.scheduleOnce(function () {
                    if (globalManager_1.levelPageController) {
                        globalManager_1.levelPageController.setCurrentLevel(selectedLevel_1);
                    }
                }, 0.1);
            }
            else {
                cc.error("无法找到 GlobalManagerController 组件！请检查场景配置。");
                cc.error("请确保场景中有节点挂载了 GlobalManagerController 组件。");
            }
        }
    };
    /**
     * 设置关卡进度（从外部调用）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectPageController.prototype.setLevelProgress = function (levelProgressData) {
        var _this = this;
        if (!this.levelSelectController)
            return;
        // 使用 LevelSelectController 的新方法来设置关卡进度
        this.levelSelectController.setLevelProgress(levelProgressData);
        // 立即更新UI显示，确保按钮状态正确
        this.scheduleOnce(function () {
            _this.updateUIDisplay();
        }, 0.05); // 很短的延迟，确保关卡数据已经更新
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectPageController.prototype.fixScrollViewScrollbar = function () {
        // 如果有ScrollView引用，清除Scrollbar引用以避免错误
        if (this.scrollView) {
            this.scrollView.horizontalScrollBar = null;
            this.scrollView.verticalScrollBar = null;
        }
        // 如果levelSelectController有ScrollView，也进行修复
        if (this.levelSelectController && this.levelSelectController.scrollView) {
            this.levelSelectController.scrollView.horizontalScrollBar = null;
            this.levelSelectController.scrollView.verticalScrollBar = null;
        }
    };
    /**
     * 更新游戏按钮显示状态
     */
    LevelSelectPageController.prototype.updateGameButtons = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (!levelData)
            return;
        // 根据关卡状态显示不同的按钮
        var isLocked = levelData.status === LevelSelectController_1.LevelStatus.LOCKED;
        if (this.startGameButton) {
            this.startGameButton.node.active = !isLocked;
            // 设置按钮文本 - 统一显示"开始游戏"
            var buttonLabel = this.startGameButton.getComponentInChildren(cc.Label);
            if (buttonLabel) {
                buttonLabel.string = "开始游戏";
            }
        }
        if (this.lockedButton) {
            this.lockedButton.node.active = isLocked;
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelSelectPageController.prototype.onStartGameButtonClick = function () {
        if (!this.levelSelectController)
            return;
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        var levelData = this.levelSelectController.getLevelData(currentLevel);
        if (levelData && levelData.status !== LevelSelectController_1.LevelStatus.LOCKED) {
            // 这里添加进入游戏的逻辑
            this.enterSelectedLevel();
        }
    };
    /**
     * 未解锁按钮点击事件
     */
    LevelSelectPageController.prototype.onLockedButtonClick = function () {
        var currentLevel = this.levelSelectController.getCurrentSelectedLevel();
        // 这里可以添加提示用户关卡未解锁的逻辑
        // 例如显示提示弹窗等
    };
    /**
     * 关卡选择变化回调
     */
    LevelSelectPageController.prototype.onLevelSelectionChanged = function (levelNumber) {
        this.updateUIDisplay();
    };
    __decorate([
        property(LevelSelectController_1.default)
    ], LevelSelectPageController.prototype, "levelSelectController", void 0);
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectPageController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelSelectPageController.prototype, "lockedButton", void 0);
    LevelSelectPageController = __decorate([
        ccclass
    ], LevelSelectPageController);
    return LevelSelectPageController;
}(cc.Component));
exports.default = LevelSelectPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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