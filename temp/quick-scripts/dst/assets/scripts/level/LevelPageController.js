
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelId: this.currentLevel
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                // 避免访问场景节点的 active 属性
                if (currentNode.name === 'game_scene' || currentNode.name === 'Scene') {
                    parentChain.push(currentNode.name + "(Scene)");
                }
                else {
                    parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                }
                currentNode = currentNode.parent;
            }
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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