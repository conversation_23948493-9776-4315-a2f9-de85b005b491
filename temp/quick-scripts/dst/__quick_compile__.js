
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":15,"./assets/meshTools/tools/MeshSdkApi":3,"./assets/meshTools/tools/Publish":17,"./assets/meshTools/tools/MeshSdk":25,"./assets/scripts/TipsDialogController":16,"./assets/scripts/ToastController":32,"./assets/scripts/GlobalManagerController":18,"./assets/scripts/bean/GameBean":4,"./assets/scripts/bean/GlobalBean":26,"./assets/scripts/bean/LanguageType":19,"./assets/scripts/bean/EnumBean":29,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":20,"./assets/scripts/common/GameTools":21,"./assets/scripts/common/MineConsole":23,"./assets/scripts/common/EventCenter":24,"./assets/scripts/game/CongratsDialogController":22,"./assets/scripts/game/GamePageController":27,"./assets/scripts/game/GameScoreController":47,"./assets/scripts/game/BtnController":28,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":30,"./assets/scripts/game/Chess/ChessBoardController":57,"./assets/scripts/hall/HallCenterLayController":31,"./assets/scripts/hall/HallCreateRoomController":34,"./assets/scripts/hall/HallJoinRoomController":40,"./assets/scripts/hall/HallPageController":33,"./assets/scripts/hall/HallParentController":37,"./assets/scripts/hall/InfoDialogController":38,"./assets/scripts/hall/KickOutDialogController":36,"./assets/scripts/hall/LeaveDialogController":63,"./assets/scripts/hall/LevelSelectDemo":35,"./assets/scripts/hall/MatchParentController":43,"./assets/scripts/hall/PlayerLayoutController":39,"./assets/scripts/hall/SettingDialogController":41,"./assets/scripts/hall/TopUpDialogController":46,"./assets/scripts/hall/HallAutoController":42,"./assets/scripts/hall/Level/LevelSelectController":53,"./assets/scripts/hall/Level/LevelSelectExample":52,"./assets/scripts/hall/Level/LevelSelectPageController":6,"./assets/scripts/hall/Level/ScrollViewHelper":44,"./assets/scripts/hall/Level/LevelItemController":45,"./assets/scripts/level/LevelPageController":55,"./assets/scripts/net/GameServerUrl":48,"./assets/scripts/net/HttpManager":10,"./assets/scripts/net/HttpUtils":49,"./assets/scripts/net/IHttpMsgBody":50,"./assets/scripts/net/MessageBaseBean":72,"./assets/scripts/net/MessageId":51,"./assets/scripts/net/WebSocketManager":54,"./assets/scripts/net/WebSocketTool":58,"./assets/scripts/net/ErrorCode":65,"./assets/scripts/pfb/InfoItemController":11,"./assets/scripts/pfb/InfoItemOneController":60,"./assets/scripts/pfb/MatchItemController":56,"./assets/scripts/pfb/PlayerGameController ":59,"./assets/scripts/pfb/PlayerScoreController":62,"./assets/scripts/pfb/SeatItemController":61,"./assets/scripts/pfb/CongratsItemController":74,"./assets/scripts/start_up/StartUpPageController":12,"./assets/scripts/start_up/StartUpCenterController":64,"./assets/scripts/test/NoticeRoundStartTest":13,"./assets/scripts/util/AudioMgr":14,"./assets/scripts/util/BlockingQueue":77,"./assets/scripts/util/Config":70,"./assets/scripts/util/Dictionary":66,"./assets/scripts/util/LocalStorageManager":67,"./assets/scripts/util/NickNameLabel":68,"./assets/scripts/util/Tools":71,"./assets/scripts/util/AudioManager":73,"./assets/meshTools/MeshTools":69,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":75,"./assets/resources/i18n/zh_CN":76},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{"../MeshTools":69,"../BaseSDK":15,"../../scripts/net/MessageBaseBean":72,"../../scripts/common/GameMgr":20,"../../scripts/common/EventCenter":24,"MeshSdk":25},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../../GlobalManagerController":18,"./LevelSelectController":53},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":69,"../../meshTools/Singleton":8,"../net/GameServerUrl":48},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"./HttpUtils":49,"./MessageBaseBean":72,"./GameServerUrl":48,"../../meshTools/MeshTools":69,"../common/GameMgr":20,"../common/EventCenter":24},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":20,"./StartUpCenterController":64},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../common/GameMgr":20,"../common/EventCenter":24,"../net/MessageId":51},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./Config":70,"./Dictionary":66},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"./util/Config":70,"./util/Tools":71},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../meshTools/MeshTools":69,"../meshTools/tools/Publish":17,"./bean/EnumBean":29,"./bean/GlobalBean":26,"./bean/LanguageType":19,"./common/EventCenter":24,"./common/GameMgr":20,"./game/GamePageController":27,"./hall/HallPageController":33,"./level/LevelPageController":55,"./hall/TopUpDialogController":46,"./net/ErrorCode":65,"./net/GameServerUrl":48,"./net/MessageBaseBean":72,"./net/MessageId":51,"./net/WebSocketManager":54,"./net/WebSocketTool":58,"./start_up/StartUpPageController":12,"./TipsDialogController":16,"./ToastController":32,"./util/AudioMgr":14,"./util/Config":70},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":3,"./EventCenter":24,"./GameData":9,"./GameTools":21,"./MineConsole":23},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../bean/GlobalBean":26,"../common/EventCenter":24,"../common/GameMgr":20,"../net/MessageBaseBean":72,"../pfb/CongratsItemController":74,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":20},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":42},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../bean/GlobalBean":26,"../hall/LeaveDialogController":63,"../util/AudioManager":73,"../util/Config":70,"../util/Tools":71,"./CongratsDialogController":22,"./GameScoreController":47,"./Chess/ChessBoardController":57,"./Chess/HexChessBoardController":30,"../net/WebSocketManager":54,"../net/MessageId":51},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../util/AudioManager":73,"../util/Config":70,"../util/LocalStorageManager":67},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../../bean/GlobalBean":26,"../../pfb/PlayerGameController ":59},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../bean/GlobalBean":26,"../net/MessageId":51,"../net/WebSocketManager":54,"../ToastController":32,"./HallAutoController":42,"./HallCreateRoomController":34,"./HallJoinRoomController":40},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../bean/GlobalBean":26,"../common/GameMgr":20,"../net/MessageId":51,"../net/WebSocketManager":54,"../net/WebSocketTool":58,"../ToastController":32,"../util/AudioManager":73,"./HallParentController":37,"./InfoDialogController":38,"./KickOutDialogController":36,"./LeaveDialogController":63,"./Level/LevelSelectPageController":6,"./MatchParentController":43,"./SettingDialogController":41},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../bean/GlobalBean":26,"../pfb/SeatItemController":61,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"./Level/LevelSelectController":53},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../net/MessageId":51,"../net/WebSocketManager":54,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../../meshTools/tools/Publish":17,"../bean/GlobalBean":26,"../common/GameMgr":20,"../net/MessageId":51,"../net/WebSocketManager":54,"../ToastController":32,"../util/Config":70,"../util/Tools":71,"./HallCenterLayController":31},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../bean/GlobalBean":26,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../meshTools/tools/Publish":17,"../util/AudioManager":73,"../util/Config":70,"../util/LocalStorageManager":67,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":26,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../../meshTools/tools/Publish":17,"../bean/GlobalBean":26,"../common/EventCenter":24,"../common/GameMgr":20,"../net/MessageBaseBean":72,"../pfb/MatchItemController":56,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./LevelSelectController":53},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../common/GameMgr":20,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../bean/GlobalBean":26,"../pfb/PlayerScoreController":62},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"./LevelSelectController":53},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"./ScrollViewHelper":44},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":24,"../common/GameMgr":20,"./WebSocketTool":58},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{"../net/WebSocketManager":54,"../net/MessageId":51,"../hall/LeaveDialogController":63,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{"../util/NickNameLabel":68,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../../bean/GlobalBean":26,"../../pfb/PlayerGameController ":59},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"./MessageBaseBean":72,"./MessageId":51,"../util/Tools":71,"../../meshTools/Singleton":8,"../common/EventCenter":24,"../common/GameMgr":20},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../util/NickNameLabel":68,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../bean/GlobalBean":26,"../util/NickNameLabel":68,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"../common/GameMgr":20,"../net/MessageId":51,"../net/WebSocketManager":54,"../util/Config":70,"../util/Tools":71},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../common/EventCenter":24,"../common/GameMgr":20,"../net/MessageBaseBean":72,"../util/Config":70},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./tools/Publish":17},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"./AudioManager":73,"./Config":70},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"./AudioMgr":14,"./LocalStorageManager":67},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"../../meshTools/tools/Publish":17,"../util/Config":70,"../util/NickNameLabel":68,"../util/Tools":71},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    