{"version": 3, "sources": ["assets/scripts/hall/Level/LevelSelectController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;;AAEtF,uDAAsD;AAEhD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAE5C,SAAS;AACT,IAAY,WAIX;AAJD,WAAY,WAAW;IACnB,iDAAU,CAAA;IACV,mDAAW,CAAA;IACX,uDAAa,CAAA,CAAG,UAAU;AAC9B,CAAC,EAJW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAItB;AASD;IAAmD,yCAAY;IAA/D;QAAA,qEA2pBC;QAxpBG,gBAAU,GAAkB,IAAI,CAAC;QAGjC,aAAO,GAAY,IAAI,CAAC;QAExB,OAAO;QACC,mBAAa,GAAgB,EAAE,CAAC;QAChC,0BAAoB,GAAW,CAAC,CAAC;QACjC,iBAAW,GAAW,EAAE,CAAC;QACzB,iBAAW,GAAW,GAAG,CAAC;QAC1B,oBAAc,GAAW,GAAG,CAAC,CAAC,eAAe;QAC7C,mBAAa,GAAW,CAAC,CAAC,CAAC,SAAS;QAE5C,iBAAiB;QACV,eAAS,GAAW,CAAC,CAAC,CAAC,gBAAgB;QACvC,iBAAW,GAAW,EAAE,CAAC,CAAC,WAAW;QACrC,yBAAmB,GAAW,CAAC,CAAC,CAAC,YAAY;QAEpD,SAAS;QACD,gBAAU,GAAc,EAAE,CAAC;QAEnC,SAAS;QACD,qBAAe,GAAY,KAAK,CAAC;QAEzC,aAAa;QACL,oBAAc,GAAW,CAAC,CAAC;QAC3B,sBAAgB,GAAW,CAAC,CAAC;QAC7B,oBAAc,GAAW,CAAC,CAAC;QAC3B,yBAAmB,GAAW,GAAG,CAAC,CAAC,SAAS;QAEpD,WAAW;QACJ,6BAAuB,GAAkC,IAAI,CAAC;;IAynBzE,CAAC;IAvnBG,sCAAM,GAAN;QACI,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,oBAAoB;QACpB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnD;IACL,CAAC;IAED,qCAAK,GAAL;QAAA,iBAOC;QANG,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC;YACd,2CAA2C;YAE3C,KAAI,CAAC,aAAa,CAAC,KAAI,CAAC,oBAAoB,CAAC,CAAC;QAClD,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,yCAAS,GAAT;QACI,UAAU;QACV,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SACtE;IACL,CAAC;IAED;;OAEG;IACK,6CAAa,GAArB;QACI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAExB,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,SAAa,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,EAAE;gBACT,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,yBAAyB;aAC1D;iBAAM;gBACH,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS;aACzC;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;SACN;QAED,UAAU;QACV,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,gDAAgB,GAAxB;QACI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,EAAE,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,SAAS;QACT,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,QAAQ;QACR,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;QACnF,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACvC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAExC,SAAS;YACT,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEhC,OAAO;YACP,IAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,GAAG,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YAC7E,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE/B,kBAAkB;YAClB,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aACvC;SACJ;IACL,CAAC;IAED;;OAEG;IACK,+CAAe,GAAvB,UAAwB,SAAoB;QACxC,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,WAAS,SAAS,CAAC,WAAa,CAAC,CAAC;QAE3D,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,kBAAkB;QAClB,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAEhD,SAAS;QACT,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU;QACtD,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC;QACxD,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC;QAEpD,QAAQ;QACR,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAEnD,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QACxB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO;QAEpC,aAAa;QACb,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,SAAS;QACT,IAAM,YAAY,GAAG,IAAI,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACrD,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QAChC,YAAY,CAAC,SAAS,GAAG,uBAAuB,CAAC;QACjD,YAAY,CAAC,OAAO,GAAG,gBAAgB,CAAC;QACxC,YAAY,CAAC,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAChE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEtC,SAAS;QACT,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,qDAAqB,GAA7B,UAA8B,UAAkB,EAAE,SAAiB;QAC/D,IAAM,MAAM,GAAG,SAAS,GAAG,EAAE,GAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,aAAa;QACzE,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAC/D,IAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,GAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,gBAAgB;QAChG,IAAM,cAAc,GAAG,IAAI,GAAG,MAAM,CAAC;QAErC,qBAAqB;QACrB,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,cAAc,GAAG,cAAc,EAAE;YACjC,aAAa,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;SACzD;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEhC,IAAM,KAAK,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC;YACzC,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACK,oDAAoB,GAA5B;QACI,IAAM,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAE5C,cAAc;QACd,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1B,UAAU;QACV,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YACnF,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;gBACnD,+BAA+B;gBAC/B,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,8CAAc,GAAtB,UAAuB,WAAmB;QACtC,OAAO,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,yDAAyB,GAAjC,UAAkC,IAAa,EAAE,SAAoB,EAAE,UAAmB;QACtF,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,6BAA6B;QAC7B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAClE,IAAM,QAAQ,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAG5C,kBAAkB;QAClB,IAAI,UAAU,EAAE;YACZ,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACvB,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,WAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,qCAAmC,QAAQ,YAAS,CAAC;oBACjE,MAAM;gBACV,KAAK,WAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,uCAAqC,QAAQ,YAAS,CAAC;oBACnE,MAAM;gBACV,KAAK,WAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,sCAAoC,QAAQ,YAAS,CAAC;oBAClE,MAAM;aACb;SACJ;aAAM;YACH,QAAQ,SAAS,CAAC,MAAM,EAAE;gBACtB,KAAK,WAAW,CAAC,MAAM;oBACnB,SAAS,GAAG,qCAAmC,QAAU,CAAC;oBAC1D,MAAM;gBACV,KAAK,WAAW,CAAC,OAAO;oBACpB,SAAS,GAAG,uCAAqC,QAAU,CAAC;oBAC5D,MAAM;gBACV,KAAK,WAAW,CAAC,SAAS;oBACtB,SAAS,GAAG,sCAAoC,QAAU,CAAC;oBAC3D,MAAM;aACb;SACJ;QAED,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE1B,UAAU;QACV,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,WAAW,EAAE,UAAC,GAAG,EAAE,WAAW;YAC1D,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE;gBACrB,MAAM,CAAC,WAAW,GAAG,WAA6B,CAAC;gBACnD,0BAA0B;gBAC1B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aAC7B;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,SAAS,EAAE;YACX,IAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,OAAO,EAAE;oBACT,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;iBACtD;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACK,kDAAkB,GAA1B;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACxC,IAAM,UAAU,GAAG,CAAC,SAAS,CAAC,WAAW,KAAK,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;SAC/D;IACL,CAAC;IAED;;OAEG;IACK,6CAAa,GAArB,UAAsB,WAAmB;QACrC,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,qDAAqB,GAA7B,UAA8B,WAAmB,EAAE,aAAqB;QACpE,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9D,yBAAyB;QACzB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAM,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;QACpC,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACxC,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;QAEnD,aAAa;QACb,IAAM,YAAY,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,GAAG,eAAe,CAAC,CAAC;QAE5F,cAAc;QACd,IAAM,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,SAAS;QACT,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACI,8CAAc,GAArB,UAAsB,KAAe,EAAE,eAAuB;QAC1D,IAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC;QAE9C,mBAAmB;QACnB,WAAW;QACX,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,UAAU;QACV,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAIhC,WAAW;QACX,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;SAC7C;QAED,gBAAgB;QAChB,gCAAgC;IACpC,CAAC;IAED;;OAEG;IACI,8CAAc,GAArB,UAAsB,WAAmB,EAAE,MAAmB;QAC1D,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;YAAE,OAAO;QAE9D,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;QACpD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,uDAAuB,GAA9B;QACI,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,4CAAY,GAAnB,UAAoB,WAAmB;QACnC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QACnE,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,gDAAgB,GAAvB,UAAwB,iBAAsB;QAG1C,IAAI,CAAC,iBAAiB,EAAE;YACpB,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAClC,OAAO;SACV;QAEO,IAAA,aAAa,GAAgC,iBAAiB,cAAjD,EAAE,YAAY,GAAkB,iBAAiB,aAAnC,EAAE,WAAW,GAAK,iBAAiB,YAAtB,CAAuB;QAGvE,UAAU;QACV,IAAI,aAAa,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,WAAW,EAAE;YACrE,EAAE,CAAC,IAAI,CAAC,gEAA2B,aAAa,uBAAkB,YAAY,sBAAiB,WAAa,CAAC,CAAC;YAC9G,OAAO;SACV;QAED,mBAAmB;QACnB,IAAI,WAAW,IAAI,WAAW,GAAG,CAAC,EAAE;YAChC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;SAClC;QAED,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,MAAM,SAAa,CAAC;YACxB,IAAI,CAAC,GAAG,YAAY,EAAE;gBAClB,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,mBAAmB;aACtD;iBAAM,IAAI,CAAC,KAAK,YAAY,EAAE;gBAC3B,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe;aAChD;iBAAM;gBACH,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,mBAAmB;aACnD;YAED,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;SAC7C;QAED,6BAA6B;QAE7B,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAG1B,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAEjC,WAAW;QACX,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAE9B,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,sDAAsB,GAA7B,UAA8B,eAAuB;QACjD,YAAY;QACZ,IAAM,iBAAiB,GAAG;YACtB,aAAa,EAAE,eAAe;YAC9B,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC;YAC7D,WAAW,EAAE,IAAI,CAAC,WAAW;SAChC,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,+CAAe,GAAtB;QACI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;gBACrD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;gBACnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;aACT;SACJ;IACL,CAAC;IAED;;OAEG;IACI,oDAAoB,GAA3B;QACI,IAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,gBAAgB,CAAC,MAAM,KAAK,WAAW,CAAC,OAAO,EAAE;YACjD,gBAAgB,CAAC,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC;YAChD,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;IACL,CAAC;IAED;;OAEG;IACK,iDAAiB,GAAzB;QACI,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,UAAU;QACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAE7D,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAGtE,CAAC;IAED;;OAEG;IACK,2CAAW,GAAnB;QACI,mBAAmB;QACnB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,OAAO;SACV;QAED,iBAAiB;QACjB,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAE1D,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YACzB,IAAM,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;YACpD,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEpE,IAAI,SAAS,GAAG,CAAC,EAAE;gBACf,IAAI,CAAC,cAAc,GAAG,WAAW,GAAG,SAAS,CAAC;aACjD;SACJ;QAED,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC;QAEtC,IAAI,CAAC,6BAA6B,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,6CAAa,GAArB;QACI,kBAAkB;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,OAAO;SACV;QAED,YAAY;QACZ,IAAM,YAAY,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEpE,wBAAwB;QACxB,IAAI,CAAC,6BAA6B,EAAE,CAAC;QAErC,mBAAmB;QACnB,6BAA6B;QAC7B,IAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE/C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;QAErE,SAAS;QACT,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,6DAA6B,GAArC;QACI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE9C,qCAAqC;QACrC,IAAM,iBAAiB,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAElD,yBAAyB;QACzB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAErC,6BAA6B;YAC7B,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC7E,IAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEpF,uBAAuB;YACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;YAEpE,IAAI,QAAQ,GAAG,WAAW,EAAE;gBACxB,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;aACxB;SACJ;QAED,mBAAmB;QACnB,IAAI,YAAY,KAAK,IAAI,CAAC,oBAAoB,EAAE;YAC5C,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;YACzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,WAAW;YACX,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;aAC9C;SACJ;IACL,CAAC;IAED;;OAEG;IACK,sDAAsB,GAA9B;QACI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;YACjC,mCAAgB,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1E,mCAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;SAC9E;IACL,CAAC;IAED;;OAEG;IACI,+CAAe,GAAtB;QAGI,WAAW;QACX,IAAM,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAG/D,SAAS;QACT,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAGjF,IAAI,cAAc,GAAG,cAAc,EAAE;YACjC,IAAM,eAAe,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;SAEjE;aAAM;SAEN;IAGL,CAAC;IAED;;OAEG;IACK,kDAAkB,GAA1B,UAA2B,OAAwB,EAAE,MAAmB;QACpE,IAAI,YAAsB,CAAC;QAC3B,QAAQ,MAAM,EAAE;YACZ,KAAK,WAAW,CAAC,MAAM;gBACnB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,WAAW,CAAC,OAAO;gBACpB,oBAAoB;gBACpB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpC,MAAM;YACV,KAAK,WAAW,CAAC,SAAS;gBACtB,iBAAiB;gBACjB,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACrC,MAAM;YACV;gBACI,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,MAAM;SACb;QAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC;QAC7B,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,iDAAiB,GAAxB;QACI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAClD,CAAC;IArpBD;QADC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;6DACS;IAGjC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACM;IANP,qBAAqB;QADzC,OAAO;OACa,qBAAqB,CA2pBzC;IAAD,4BAAC;CA3pBD,AA2pBC,CA3pBkD,EAAE,CAAC,SAAS,GA2pB9D;kBA3pBoB,qBAAqB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { ScrollViewHelper } from \"./ScrollViewHelper\";\n\nconst { ccclass, property } = cc._decorator;\n\n// 关卡状态枚举\nexport enum LevelStatus {\n    LOCKED = 0,     // 未解锁（灰色）\n    CURRENT = 1,    // 正在进行（黄色）\n    COMPLETED = 2   // 已通关（绿色）\n}\n\n// 关卡数据接口\nexport interface LevelData {\n    levelNumber: number;\n    status: LevelStatus;\n}\n\n@ccclass\nexport default class LevelSelectController extends cc.Component {\n\n    @property(cc.ScrollView)\n    scrollView: cc.ScrollView = null;\n\n    @property(cc.Node)\n    content: cc.Node = null;\n\n    // 关卡数据\n    private levelDataList: LevelData[] = [];\n    private currentSelectedLevel: number = 1;\n    private totalLevels: number = 30;\n    private screenWidth: number = 650;\n    private levelItemWidth: number = 150; // 关卡项的宽度（包括间距）\n    private visibleLevels: number = 3; // 可见关卡数量\n\n    // 连接线配置（公开，方便调试）\n    public lineCount: number = 9; // 每两个关卡之间的连接线数量\n    public lineSpacing: number = 16; // 连接线之间的间距\n    public levelToLineDistance: number = 8; // 关卡到连接线的距离\n\n    // 关卡节点列表\n    private levelNodes: cc.Node[] = [];\n\n    // 滑动状态标志\n    private isAutoScrolling: boolean = false;\n\n    // 快速滑动检测相关变量\n    private lastScrollTime: number = 0;\n    private lastScrollOffset: number = 0;\n    private scrollVelocity: number = 0;\n    private fastScrollThreshold: number = 0.5; // 快速滑动阈值\n\n    // 关卡选择变化回调\n    public onLevelSelectionChanged: (levelNumber: number) => void = null;\n\n    onLoad() {\n        // 修复ScrollView的Scrollbar问题\n        this.fixScrollViewScrollbar();\n\n        this.initLevelData();\n        this.createLevelItems();\n        this.updateLevelDisplay();\n        this.setupScrollEvents();\n\n        // 设置初始滚动位置为第1关（最左边）\n        if (this.scrollView) {\n            this.scrollView.scrollToPercentHorizontal(0, 0);\n        }\n    }\n\n    start() {\n        // 延迟滚动到当前选中关卡，确保界面完全初始化\n        this.scheduleOnce(() => {\n            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）\n           \n            this.scrollToLevel(this.currentSelectedLevel);\n        }, 0.1);\n    }\n\n    onDestroy() {\n        // 移除事件监听器\n        if (this.scrollView) {\n            this.scrollView.node.off('scrolling', this.onScrolling, this);\n            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);\n        }\n    }\n\n    /**\n     * 初始化关卡数据\n     */\n    private initLevelData() {\n        this.levelDataList = [];\n\n        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态\n        for (let i = 1; i <= this.totalLevels; i++) {\n            let status: LevelStatus;\n            if (i === 1) {\n                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）\n            } else {\n                status = LevelStatus.LOCKED; // 其他关卡锁定\n            }\n\n            this.levelDataList.push({\n                levelNumber: i,\n                status: status\n            });\n        }\n\n        // 默认选中第一关\n        this.currentSelectedLevel = 1;\n    }\n\n    /**\n     * 创建关卡项目\n     */\n    private createLevelItems() {\n        if (!this.content) {\n            cc.error(\"Content node is not assigned!\");\n            return;\n        }\n\n        // 清空现有内容\n        this.content.removeAllChildren();\n        this.levelNodes = [];\n\n        // 计算总宽度\n        const totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;\n        this.content.width = totalWidth;\n\n        for (let i = 0; i < this.totalLevels; i++) {\n            const levelData = this.levelDataList[i];\n            \n            // 创建关卡节点\n            const levelNode = this.createLevelNode(levelData);\n            this.content.addChild(levelNode);\n            this.levelNodes.push(levelNode);\n\n            // 设置位置\n            const posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;\n            levelNode.setPosition(posX, 0);\n\n            // 创建连接线（除了最后一个关卡）\n            if (i < this.totalLevels - 1) {\n                this.createConnectionLines(i, posX);\n            }\n        }\n    }\n\n    /**\n     * 创建关卡节点\n     */\n    private createLevelNode(levelData: LevelData): cc.Node {\n        const node = new cc.Node(`Level_${levelData.levelNumber}`);\n        \n        // 添加Sprite组件\n        const sprite = node.addComponent(cc.Sprite);\n        \n        // 添加Label组件显示关卡数字\n        const labelNode = new cc.Node(\"LevelLabel\");\n        const label = labelNode.addComponent(cc.Label);\n        label.string = levelData.levelNumber.toString();\n\n        // 设置字体样式\n        label.fontSize = 30;\n        label.node.color = cc.color(255, 255, 255); // #FFFFFF\n        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;\n        label.verticalAlign = cc.Label.VerticalAlign.CENTER;\n\n        // 添加外边框\n        const outline = label.addComponent(cc.LabelOutline);\n        this.updateLabelOutline(outline, levelData.status);\n\n        labelNode.parent = node;\n        labelNode.setPosition(0, 0); // 居中对齐\n\n        // 添加Button组件\n        const button = node.addComponent(cc.Button);\n        button.target = node;\n\n        // 设置点击事件\n        const eventHandler = new cc.Component.EventHandler();\n        eventHandler.target = this.node;\n        eventHandler.component = \"LevelSelectController\";\n        eventHandler.handler = \"onLevelClicked\";\n        eventHandler.customEventData = levelData.levelNumber.toString();\n        button.clickEvents.push(eventHandler);\n\n        // 更新关卡外观\n        this.updateLevelNodeAppearance(node, levelData, false);\n\n        return node;\n    }\n\n    /**\n     * 创建连接线组（9个连接线）\n     */\n    private createConnectionLines(levelIndex: number, levelPosX: number) {\n        const startX = levelPosX + 46/2 + this.levelToLineDistance; // 关卡右边缘 + 距离\n        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;\n        const endX = levelPosX + this.levelItemWidth - 46/2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离\n        const availableWidth = endX - startX;\n\n        // 如果可用宽度小于需要的宽度，调整间距\n        let actualSpacing = this.lineSpacing;\n        if (totalLineWidth > availableWidth) {\n            actualSpacing = availableWidth / (this.lineCount - 1);\n        }\n\n        for (let i = 0; i < this.lineCount; i++) {\n            const lineNode = this.createSingleLineNode();\n            this.content.addChild(lineNode);\n\n            const lineX = startX + i * actualSpacing;\n            lineNode.setPosition(lineX, 0);\n        }\n    }\n\n    /**\n     * 创建单个连接线节点\n     */\n    private createSingleLineNode(): cc.Node {\n        const node = new cc.Node(\"Line\");\n        const sprite = node.addComponent(cc.Sprite);\n\n        // 设置连接线大小为6*6\n        node.setContentSize(6, 6);\n\n        // 加载连接线图片\n        cc.resources.load(\"hall_page_res/Level_Btn/pop_line\", cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖\n                node.setContentSize(6, 6);\n            }\n        });\n\n        return node;\n    }\n\n    /**\n     * 检查是否为特殊关卡（第5、10、15、20、25关）\n     */\n    private isSpecialLevel(levelNumber: number): boolean {\n        return levelNumber % 5 === 0;\n    }\n\n    /**\n     * 更新关卡节点外观\n     */\n    private updateLevelNodeAppearance(node: cc.Node, levelData: LevelData, isSelected: boolean) {\n        const sprite = node.getComponent(cc.Sprite);\n        if (!sprite) return;\n\n        let imagePath = \"\";\n        let size = cc.size(46, 46);\n\n        // 检查是否为特殊关卡（第5、10、15、20、25关）\n        const isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);\n        const uiSuffix = isSpecialLevel ? \"01\" : \"\";\n\n\n        // 根据状态和是否选中确定图片路径\n        if (isSelected) {\n            size = cc.size(86, 86);\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}_choose`;\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}_choose`;\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}_choose`;\n                    break;\n            }\n        } else {\n            switch (levelData.status) {\n                case LevelStatus.LOCKED:\n                    imagePath = `hall_page_res/Level_Btn/pop_gray${uiSuffix}`;\n                    break;\n                case LevelStatus.CURRENT:\n                    imagePath = `hall_page_res/Level_Btn/pop_yellow${uiSuffix}`;\n                    break;\n                case LevelStatus.COMPLETED:\n                    imagePath = `hall_page_res/Level_Btn/pop_green${uiSuffix}`;\n                    break;\n            }\n        }\n\n        // 设置节点大小\n        node.setContentSize(size);\n\n        // 加载并设置图片\n        cc.resources.load(imagePath, cc.SpriteFrame, (err, spriteFrame) => {\n            if (!err && spriteFrame) {\n                sprite.spriteFrame = spriteFrame as cc.SpriteFrame;\n                // 图片加载后强制设置正确的大小，覆盖图片原始大小\n                node.setContentSize(size);\n            }\n        });\n\n        // 更新标签外边框\n        const labelNode = node.getChildByName(\"LevelLabel\");\n        if (labelNode) {\n            const label = labelNode.getComponent(cc.Label);\n            if (label) {\n                const outline = label.getComponent(cc.LabelOutline);\n                if (outline) {\n                    this.updateLabelOutline(outline, levelData.status);\n                }\n            }\n        }\n    }\n\n    /**\n     * 更新关卡显示\n     */\n    private updateLevelDisplay() {\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const node = this.levelNodes[i];\n            const levelData = this.levelDataList[i];\n            const isSelected = (levelData.levelNumber === this.currentSelectedLevel);\n            \n            this.updateLevelNodeAppearance(node, levelData, isSelected);\n        }\n    }\n\n    /**\n     * 滚动到指定关卡\n     */\n    private scrollToLevel(levelNumber: number) {\n        this.scrollToLevelWithTime(levelNumber, 0.3);\n    }\n\n    /**\n     * 滚动到指定关卡（带自定义动画时间）\n     */\n    private scrollToLevelWithTime(levelNumber: number, animationTime: number) {\n        if (levelNumber < 1 || levelNumber > this.totalLevels) return;\n\n        // 设置自动滚动标志，防止滚动过程中触发位置更新\n        this.isAutoScrolling = true;\n\n        const targetIndex = levelNumber - 1;\n        const contentWidth = this.content.width;\n        const scrollViewWidth = this.scrollView.node.width;\n\n        // 计算目标位置的偏移量\n        const targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);\n\n        // 限制偏移量在有效范围内\n        const clampedOffset = cc.misc.clampf(targetOffset, 0, 1);\n\n        // 设置滚动位置\n        this.scrollView.scrollToPercentHorizontal(clampedOffset, animationTime);\n    }\n\n    /**\n     * 关卡点击事件处理\n     */\n    public onLevelClicked(event: cc.Event, customEventData: string) {\n        const levelNumber = parseInt(customEventData);\n\n        // 允许选择任何关卡（包括未解锁的）\n        // 更新当前选中关卡\n        this.currentSelectedLevel = levelNumber;\n        this.updateLevelDisplay();\n\n        // 滚动到选中关卡\n        this.isAutoScrolling = true;\n        this.scrollToLevel(levelNumber);\n\n      \n\n        // 通知关卡选择变化\n        if (this.onLevelSelectionChanged) {\n            this.onLevelSelectionChanged(levelNumber);\n        }\n\n        // 这里可以添加进入关卡的逻辑\n        // this.enterLevel(levelNumber);\n    }\n\n    /**\n     * 设置关卡状态\n     */\n    public setLevelStatus(levelNumber: number, status: LevelStatus) {\n        if (levelNumber < 1 || levelNumber > this.totalLevels) return;\n\n        this.levelDataList[levelNumber - 1].status = status;\n        this.updateLevelDisplay();\n    }\n\n    /**\n     * 获取当前选中的关卡\n     */\n    public getCurrentSelectedLevel(): number {\n        return this.currentSelectedLevel;\n    }\n\n    /**\n     * 获取指定关卡的数据\n     */\n    public getLevelData(levelNumber: number): LevelData | null {\n        if (levelNumber < 1 || levelNumber > this.totalLevels) return null;\n        return this.levelDataList[levelNumber - 1];\n    }\n\n    /**\n     * 设置关卡进度（从外部调用，比如从后端获取数据后）\n     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels\n     */\n    public setLevelProgress(levelProgressData: any) {\n        \n\n        if (!levelProgressData) {\n            cc.warn(\"❌ levelProgressData 为空\");\n            return;\n        }\n\n        const { clearedLevels, currentLevel, totalLevels } = levelProgressData;\n       \n\n        // 验证数据有效性\n        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {\n            cc.warn(`❌ 数据验证失败: clearedLevels=${clearedLevels}, currentLevel=${currentLevel}, totalLevels=${totalLevels}`);\n            return;\n        }\n\n        // 更新总关卡数（如果后端传了的话）\n        if (totalLevels && totalLevels > 0) {\n            this.totalLevels = totalLevels;\n        }\n\n        // 重新设置所有关卡状态\n        for (let i = 1; i <= this.totalLevels; i++) {\n            let status: LevelStatus;\n            if (i < currentLevel) {\n                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）\n            } else if (i === currentLevel) {\n                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）\n            } else {\n                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）\n            }\n\n            this.levelDataList[i - 1].status = status;\n        }\n\n        // 更新当前选中关卡为后端指定的currentLevel\n       \n        this.currentSelectedLevel = currentLevel;\n        this.updateLevelDisplay();\n\n       \n        // scrollToLevel 方法内部会设置 isAutoScrolling = true\n        this.scrollToLevel(currentLevel);\n\n        // 通知关卡选择变化\n        if (this.onLevelSelectionChanged) {\n        \n            this.onLevelSelectionChanged(currentLevel);\n        }\n    }\n\n    /**\n     * 设置关卡进度（兼容旧接口）\n     * @param completedLevels 已完成的关卡数\n     */\n    public setLevelProgressLegacy(completedLevels: number) {\n        // 转换为新的数据格式\n        const levelProgressData = {\n            clearedLevels: completedLevels,\n            currentLevel: Math.min(completedLevels + 1, this.totalLevels),\n            totalLevels: this.totalLevels\n        };\n        this.setLevelProgress(levelProgressData);\n    }\n\n    /**\n     * 解锁下一关\n     */\n    public unlockNextLevel() {\n        for (let i = 0; i < this.levelDataList.length; i++) {\n            if (this.levelDataList[i].status === LevelStatus.LOCKED) {\n                this.levelDataList[i].status = LevelStatus.CURRENT;\n                this.updateLevelDisplay();\n                break;\n            }\n        }\n    }\n\n    /**\n     * 完成当前关卡\n     */\n    public completeCurrentLevel() {\n        const currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];\n        if (currentLevelData.status === LevelStatus.CURRENT) {\n            currentLevelData.status = LevelStatus.COMPLETED;\n            this.unlockNextLevel();\n            this.updateLevelDisplay();\n        }\n    }\n\n    /**\n     * 设置滑动事件监听\n     */\n    private setupScrollEvents() {\n        if (!this.scrollView) return;\n\n        // 监听滑动中事件\n        this.scrollView.node.on('scrolling', this.onScrolling, this);\n\n        // 监听滑动结束事件\n        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);\n\n     \n    }\n\n    /**\n     * 滑动中事件处理\n     */\n    private onScrolling() {\n        // 如果是自动滚动，不要更新选中关卡\n        if (this.isAutoScrolling) {\n            return;\n        }\n\n        // 记录滑动速度用于快速滑动检测\n        const currentTime = Date.now();\n        const currentOffset = this.scrollView.getScrollOffset().x;\n\n        if (this.lastScrollTime > 0) {\n            const deltaTime = currentTime - this.lastScrollTime;\n            const deltaOffset = Math.abs(currentOffset - this.lastScrollOffset);\n\n            if (deltaTime > 0) {\n                this.scrollVelocity = deltaOffset / deltaTime;\n            }\n        }\n\n        this.lastScrollTime = currentTime;\n        this.lastScrollOffset = currentOffset;\n\n        this.updateSelectedLevelByPosition();\n    }\n\n    /**\n     * 滑动结束事件处理\n     */\n    private onScrollEnded() {\n        // 如果是自动滚动触发的事件，忽略\n        if (this.isAutoScrolling) {\n            this.isAutoScrolling = false;\n            return;\n        }\n\n        // 检测是否为快速滑动\n        const isFastScroll = this.scrollVelocity > this.fastScrollThreshold;\n\n        // 无论是否快速滑动，都要找到最接近中心的关卡\n        this.updateSelectedLevelByPosition();\n\n        // 滑动结束后，将选中的关卡固定居中\n        // 如果是快速滑动，使用较短的动画时间以提供更好的响应感\n        const animationTime = isFastScroll ? 0.2 : 0.3;\n\n        this.isAutoScrolling = true;\n        this.scrollToLevelWithTime(this.currentSelectedLevel, animationTime);\n\n        // 重置滑动速度\n        this.scrollVelocity = 0;\n        this.lastScrollTime = 0;\n    }\n\n    /**\n     * 根据当前位置更新选中的关卡\n     */\n    private updateSelectedLevelByPosition() {\n        if (!this.scrollView || !this.content) return;\n\n        // 获取ScrollView的中心位置（相对于ScrollView节点）\n        const scrollViewCenterX = 0; // ScrollView的中心就是x=0\n\n        // 找到最接近ScrollView中心位置的关卡\n        let closestLevel = 1;\n        let minDistance = Number.MAX_VALUE;\n\n        for (let i = 0; i < this.levelNodes.length; i++) {\n            const levelNode = this.levelNodes[i];\n\n            // 将关卡节点的本地坐标转换为ScrollView坐标系\n            const levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);\n            const levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);\n\n            // 计算关卡与ScrollView中心的距离\n            const distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);\n\n            if (distance < minDistance) {\n                minDistance = distance;\n                closestLevel = i + 1;\n            }\n        }\n\n        // 如果选中的关卡发生变化，更新显示\n        if (closestLevel !== this.currentSelectedLevel) {\n            this.currentSelectedLevel = closestLevel;\n            this.updateLevelDisplay();\n\n            // 通知关卡选择变化\n            if (this.onLevelSelectionChanged) {\n                this.onLevelSelectionChanged(closestLevel);\n            }\n        }\n    }\n\n    /**\n     * 修复ScrollView的Scrollbar问题\n     */\n    private fixScrollViewScrollbar() {\n        if (this.scrollView && this.content) {\n            ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);\n            ScrollViewHelper.debugScrollView(this.scrollView, \"LevelSelectController\");\n        }\n    }\n\n    /**\n     * 调试参数信息\n     */\n    public debugParameters() {\n       \n\n        // 计算连接线总宽度\n        const totalLineWidth = (this.lineCount - 1) * this.lineSpacing;\n      \n\n        // 计算可用宽度\n        const availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);\n      \n\n        if (totalLineWidth > availableWidth) {\n            const adjustedSpacing = availableWidth / (this.lineCount - 1);\n           \n        } else {\n   \n        }\n\n      \n    }\n\n    /**\n     * 更新标签外边框\n     */\n    private updateLabelOutline(outline: cc.LabelOutline, status: LevelStatus) {\n        let outlineColor: cc.Color;\n        switch (status) {\n            case LevelStatus.LOCKED:\n                // 未解锁边框为 #7B7B7B\n                outlineColor = cc.color(123, 123, 123);\n                break;\n            case LevelStatus.CURRENT:\n                // 当前玩到的关卡边框 #CF5800\n                outlineColor = cc.color(207, 88, 0);\n                break;\n            case LevelStatus.COMPLETED:\n                // 已解锁边框为 #119C0F\n                outlineColor = cc.color(17, 156, 15);\n                break;\n            default:\n                outlineColor = cc.color(123, 123, 123);\n                break;\n        }\n\n        outline.color = outlineColor;\n        outline.width = 1;\n    }\n\n    /**\n     * 重新创建关卡项目（用于参数调整后刷新）\n     */\n    public refreshLevelItems() {\n        this.createLevelItems();\n        this.updateLevelDisplay();\n        this.scrollToLevel(this.currentSelectedLevel);\n    }\n\n\n}\n"]}