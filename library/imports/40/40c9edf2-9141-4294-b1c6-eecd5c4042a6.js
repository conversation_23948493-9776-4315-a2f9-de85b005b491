"use strict";
cc._RF.push(module, '40c9e3ykUFClLHG7s1cQEKm', 'LevelSelectController');
// scripts/hall/Level/LevelSelectController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LevelStatus = void 0;
var ScrollViewHelper_1 = require("./ScrollViewHelper");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 关卡状态枚举
var LevelStatus;
(function (LevelStatus) {
    LevelStatus[LevelStatus["LOCKED"] = 0] = "LOCKED";
    LevelStatus[LevelStatus["CURRENT"] = 1] = "CURRENT";
    LevelStatus[LevelStatus["COMPLETED"] = 2] = "COMPLETED"; // 已通关（绿色）
})(LevelStatus = exports.LevelStatus || (exports.LevelStatus = {}));
var LevelSelectController = /** @class */ (function (_super) {
    __extends(LevelSelectController, _super);
    function LevelSelectController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scrollView = null;
        _this.content = null;
        // 关卡数据
        _this.levelDataList = [];
        _this.currentSelectedLevel = 1;
        _this.totalLevels = 30;
        _this.screenWidth = 650;
        _this.levelItemWidth = 150; // 关卡项的宽度（包括间距）
        _this.visibleLevels = 3; // 可见关卡数量
        // 连接线配置（公开，方便调试）
        _this.lineCount = 9; // 每两个关卡之间的连接线数量
        _this.lineSpacing = 16; // 连接线之间的间距
        _this.levelToLineDistance = 8; // 关卡到连接线的距离
        // 关卡节点列表
        _this.levelNodes = [];
        // 滑动状态标志
        _this.isAutoScrolling = false;
        // 关卡选择变化回调
        _this.onLevelSelectionChanged = null;
        return _this;
    }
    LevelSelectController.prototype.onLoad = function () {
        // 修复ScrollView的Scrollbar问题
        this.fixScrollViewScrollbar();
        this.initLevelData();
        this.createLevelItems();
        this.updateLevelDisplay();
        this.setupScrollEvents();
        // 设置初始滚动位置为第1关（最左边）
        if (this.scrollView) {
            this.scrollView.scrollToPercentHorizontal(0, 0);
        }
    };
    LevelSelectController.prototype.start = function () {
        var _this = this;
        // 延迟滚动到当前选中关卡，确保界面完全初始化
        this.scheduleOnce(function () {
            // 滚动到当前选中的关卡（可能已经通过ExtendLevelProgress设置了）
            _this.scrollToLevel(_this.currentSelectedLevel);
        }, 0.1);
    };
    LevelSelectController.prototype.onDestroy = function () {
        // 移除事件监听器
        if (this.scrollView) {
            this.scrollView.node.off('scrolling', this.onScrolling, this);
            this.scrollView.node.off('scroll-ended', this.onScrollEnded, this);
        }
    };
    /**
     * 初始化关卡数据
     */
    LevelSelectController.prototype.initLevelData = function () {
        this.levelDataList = [];
        // 初始化关卡状态：第1关为当前可玩关卡，其他为锁定状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i === 1) {
                status = LevelStatus.CURRENT; // 第1关默认为当前可玩关卡（显示开始游戏按钮）
            }
            else {
                status = LevelStatus.LOCKED; // 其他关卡锁定
            }
            this.levelDataList.push({
                levelNumber: i,
                status: status
            });
        }
        // 默认选中第一关
        this.currentSelectedLevel = 1;
    };
    /**
     * 创建关卡项目
     */
    LevelSelectController.prototype.createLevelItems = function () {
        if (!this.content) {
            cc.error("Content node is not assigned!");
            return;
        }
        // 清空现有内容
        this.content.removeAllChildren();
        this.levelNodes = [];
        // 计算总宽度
        var totalWidth = (this.totalLevels - 1) * this.levelItemWidth + this.screenWidth;
        this.content.width = totalWidth;
        for (var i = 0; i < this.totalLevels; i++) {
            var levelData = this.levelDataList[i];
            // 创建关卡节点
            var levelNode = this.createLevelNode(levelData);
            this.content.addChild(levelNode);
            this.levelNodes.push(levelNode);
            // 设置位置
            var posX = i * this.levelItemWidth - totalWidth / 2 + this.screenWidth / 2;
            levelNode.setPosition(posX, 0);
            // 创建连接线（除了最后一个关卡）
            if (i < this.totalLevels - 1) {
                this.createConnectionLines(i, posX);
            }
        }
    };
    /**
     * 创建关卡节点
     */
    LevelSelectController.prototype.createLevelNode = function (levelData) {
        var node = new cc.Node("Level_" + levelData.levelNumber);
        // 添加Sprite组件
        var sprite = node.addComponent(cc.Sprite);
        // 添加Label组件显示关卡数字
        var labelNode = new cc.Node("LevelLabel");
        var label = labelNode.addComponent(cc.Label);
        label.string = levelData.levelNumber.toString();
        // 设置字体样式
        label.fontSize = 30;
        label.node.color = cc.color(255, 255, 255); // #FFFFFF
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 添加外边框
        var outline = label.addComponent(cc.LabelOutline);
        this.updateLabelOutline(outline, levelData.status);
        labelNode.parent = node;
        labelNode.setPosition(0, 0); // 居中对齐
        // 添加Button组件
        var button = node.addComponent(cc.Button);
        button.target = node;
        // 设置点击事件
        var eventHandler = new cc.Component.EventHandler();
        eventHandler.target = this.node;
        eventHandler.component = "LevelSelectController";
        eventHandler.handler = "onLevelClicked";
        eventHandler.customEventData = levelData.levelNumber.toString();
        button.clickEvents.push(eventHandler);
        // 更新关卡外观
        this.updateLevelNodeAppearance(node, levelData, false);
        return node;
    };
    /**
     * 创建连接线组（9个连接线）
     */
    LevelSelectController.prototype.createConnectionLines = function (levelIndex, levelPosX) {
        var startX = levelPosX + 46 / 2 + this.levelToLineDistance; // 关卡右边缘 + 距离
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        var endX = levelPosX + this.levelItemWidth - 46 / 2 - this.levelToLineDistance; // 下一个关卡左边缘 - 距离
        var availableWidth = endX - startX;
        // 如果可用宽度小于需要的宽度，调整间距
        var actualSpacing = this.lineSpacing;
        if (totalLineWidth > availableWidth) {
            actualSpacing = availableWidth / (this.lineCount - 1);
        }
        for (var i = 0; i < this.lineCount; i++) {
            var lineNode = this.createSingleLineNode();
            this.content.addChild(lineNode);
            var lineX = startX + i * actualSpacing;
            lineNode.setPosition(lineX, 0);
        }
    };
    /**
     * 创建单个连接线节点
     */
    LevelSelectController.prototype.createSingleLineNode = function () {
        var node = new cc.Node("Line");
        var sprite = node.addComponent(cc.Sprite);
        // 设置连接线大小为6*6
        node.setContentSize(6, 6);
        // 加载连接线图片
        cc.resources.load("hall_page_res/Level_Btn/pop_line", cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                // 图片加载后重新设置大小为6x6，确保不被图片原始大小覆盖
                node.setContentSize(6, 6);
            }
        });
        return node;
    };
    /**
     * 检查是否为特殊关卡（第5、10、15、20、25关）
     */
    LevelSelectController.prototype.isSpecialLevel = function (levelNumber) {
        return levelNumber % 5 === 0;
    };
    /**
     * 更新关卡节点外观
     */
    LevelSelectController.prototype.updateLevelNodeAppearance = function (node, levelData, isSelected) {
        var sprite = node.getComponent(cc.Sprite);
        if (!sprite)
            return;
        var imagePath = "";
        var size = cc.size(46, 46);
        // 检查是否为特殊关卡（第5、10、15、20、25关）
        var isSpecialLevel = this.isSpecialLevel(levelData.levelNumber);
        var uiSuffix = isSpecialLevel ? "01" : "";
        // 根据状态和是否选中确定图片路径
        if (isSelected) {
            size = cc.size(86, 86);
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix + "_choose";
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix + "_choose";
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix + "_choose";
                    break;
            }
        }
        else {
            switch (levelData.status) {
                case LevelStatus.LOCKED:
                    imagePath = "hall_page_res/Level_Btn/pop_gray" + uiSuffix;
                    break;
                case LevelStatus.CURRENT:
                    imagePath = "hall_page_res/Level_Btn/pop_yellow" + uiSuffix;
                    break;
                case LevelStatus.COMPLETED:
                    imagePath = "hall_page_res/Level_Btn/pop_green" + uiSuffix;
                    break;
            }
        }
        // 设置节点大小
        node.setContentSize(size);
        // 记录期望的图片路径和选中状态，用于异步加载完成时验证
        node._expectedImagePath = imagePath;
        node._expectedSelected = isSelected;
        // 加载并设置图片
        cc.resources.load(imagePath, cc.SpriteFrame, function (err, spriteFrame) {
            if (!err && spriteFrame && sprite) {
                // 检查节点是否仍然期望这个图片（避免异步加载竞争导致的状态错误）
                var currentExpectedPath = node._expectedImagePath;
                var currentExpectedSelected = node._expectedSelected;
                if (currentExpectedPath === imagePath && currentExpectedSelected === isSelected) {
                    sprite.spriteFrame = spriteFrame;
                    // 图片加载后强制设置正确的大小，覆盖图片原始大小
                    node.setContentSize(size);
                }
            }
        });
        // 更新标签外边框
        var labelNode = node.getChildByName("LevelLabel");
        if (labelNode) {
            var label = labelNode.getComponent(cc.Label);
            if (label) {
                var outline = label.getComponent(cc.LabelOutline);
                if (outline) {
                    this.updateLabelOutline(outline, levelData.status);
                }
            }
        }
    };
    /**
     * 更新关卡显示
     */
    LevelSelectController.prototype.updateLevelDisplay = function () {
        for (var i = 0; i < this.levelNodes.length; i++) {
            var node = this.levelNodes[i];
            var levelData = this.levelDataList[i];
            var isSelected = (levelData.levelNumber === this.currentSelectedLevel);
            this.updateLevelNodeAppearance(node, levelData, isSelected);
        }
    };
    /**
     * 滚动到指定关卡
     */
    LevelSelectController.prototype.scrollToLevel = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        // 设置自动滚动标志，防止滚动过程中触发位置更新
        this.isAutoScrolling = true;
        var targetIndex = levelNumber - 1;
        var contentWidth = this.content.width;
        var scrollViewWidth = this.scrollView.node.width;
        // 计算目标位置的偏移量
        var targetOffset = (targetIndex * this.levelItemWidth) / (contentWidth - scrollViewWidth);
        // 限制偏移量在有效范围内
        var clampedOffset = cc.misc.clampf(targetOffset, 0, 1);
        // 设置滚动位置
        this.scrollView.scrollToPercentHorizontal(clampedOffset, 0.3);
    };
    /**
     * 关卡点击事件处理
     */
    LevelSelectController.prototype.onLevelClicked = function (event, customEventData) {
        var levelNumber = parseInt(customEventData);
        // 允许选择任何关卡（包括未解锁的）
        // 更新当前选中关卡
        this.currentSelectedLevel = levelNumber;
        this.updateLevelDisplay();
        // 滚动到选中关卡
        this.isAutoScrolling = true;
        this.scrollToLevel(levelNumber);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(levelNumber);
        }
        // 这里可以添加进入关卡的逻辑
        // this.enterLevel(levelNumber);
    };
    /**
     * 设置关卡状态
     */
    LevelSelectController.prototype.setLevelStatus = function (levelNumber, status) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return;
        this.levelDataList[levelNumber - 1].status = status;
        this.updateLevelDisplay();
    };
    /**
     * 获取当前选中的关卡
     */
    LevelSelectController.prototype.getCurrentSelectedLevel = function () {
        return this.currentSelectedLevel;
    };
    /**
     * 获取指定关卡的数据
     */
    LevelSelectController.prototype.getLevelData = function (levelNumber) {
        if (levelNumber < 1 || levelNumber > this.totalLevels)
            return null;
        return this.levelDataList[levelNumber - 1];
    };
    /**
     * 设置关卡进度（从外部调用，比如从后端获取数据后）
     * @param levelProgressData 关卡进度数据，包含clearedLevels, currentLevel, totalLevels
     */
    LevelSelectController.prototype.setLevelProgress = function (levelProgressData) {
        if (!levelProgressData) {
            cc.warn("❌ levelProgressData 为空");
            return;
        }
        var clearedLevels = levelProgressData.clearedLevels, currentLevel = levelProgressData.currentLevel, totalLevels = levelProgressData.totalLevels;
        // 验证数据有效性
        if (clearedLevels < 0 || currentLevel < 1 || currentLevel > totalLevels) {
            cc.warn("\u274C \u6570\u636E\u9A8C\u8BC1\u5931\u8D25: clearedLevels=" + clearedLevels + ", currentLevel=" + currentLevel + ", totalLevels=" + totalLevels);
            return;
        }
        // 更新总关卡数（如果后端传了的话）
        if (totalLevels && totalLevels > 0) {
            this.totalLevels = totalLevels;
        }
        // 重新设置所有关卡状态
        for (var i = 1; i <= this.totalLevels; i++) {
            var status = void 0;
            if (i < currentLevel) {
                status = LevelStatus.COMPLETED; // 当前关卡之前的都是已完成（绿色）
            }
            else if (i === currentLevel) {
                status = LevelStatus.CURRENT; // 当前关卡（黄色选择UI）
            }
            else {
                status = LevelStatus.LOCKED; // 当前关卡之后的都是未解锁（灰色）
            }
            this.levelDataList[i - 1].status = status;
        }
        // 更新当前选中关卡为后端指定的currentLevel
        this.currentSelectedLevel = currentLevel;
        this.updateLevelDisplay();
        // scrollToLevel 方法内部会设置 isAutoScrolling = true
        this.scrollToLevel(currentLevel);
        // 通知关卡选择变化
        if (this.onLevelSelectionChanged) {
            this.onLevelSelectionChanged(currentLevel);
        }
    };
    /**
     * 设置关卡进度（兼容旧接口）
     * @param completedLevels 已完成的关卡数
     */
    LevelSelectController.prototype.setLevelProgressLegacy = function (completedLevels) {
        // 转换为新的数据格式
        var levelProgressData = {
            clearedLevels: completedLevels,
            currentLevel: Math.min(completedLevels + 1, this.totalLevels),
            totalLevels: this.totalLevels
        };
        this.setLevelProgress(levelProgressData);
    };
    /**
     * 解锁下一关
     */
    LevelSelectController.prototype.unlockNextLevel = function () {
        for (var i = 0; i < this.levelDataList.length; i++) {
            if (this.levelDataList[i].status === LevelStatus.LOCKED) {
                this.levelDataList[i].status = LevelStatus.CURRENT;
                this.updateLevelDisplay();
                break;
            }
        }
    };
    /**
     * 完成当前关卡
     */
    LevelSelectController.prototype.completeCurrentLevel = function () {
        var currentLevelData = this.levelDataList[this.currentSelectedLevel - 1];
        if (currentLevelData.status === LevelStatus.CURRENT) {
            currentLevelData.status = LevelStatus.COMPLETED;
            this.unlockNextLevel();
            this.updateLevelDisplay();
        }
    };
    /**
     * 设置滑动事件监听
     */
    LevelSelectController.prototype.setupScrollEvents = function () {
        if (!this.scrollView)
            return;
        // 监听滑动中事件
        this.scrollView.node.on('scrolling', this.onScrolling, this);
        // 监听滑动结束事件
        this.scrollView.node.on('scroll-ended', this.onScrollEnded, this);
    };
    /**
     * 滑动中事件处理
     */
    LevelSelectController.prototype.onScrolling = function () {
        // 如果是自动滚动，不要更新选中关卡
        if (this.isAutoScrolling) {
            return;
        }
        this.updateSelectedLevelByPosition();
    };
    /**
     * 滑动结束事件处理
     */
    LevelSelectController.prototype.onScrollEnded = function () {
        // 如果是自动滚动触发的事件，忽略
        if (this.isAutoScrolling) {
            this.isAutoScrolling = false;
            return;
        }
        this.updateSelectedLevelByPosition();
        // 滑动结束后，将选中的关卡固定居中
        this.isAutoScrolling = true;
        this.scrollToLevel(this.currentSelectedLevel);
    };
    /**
     * 根据当前位置更新选中的关卡
     */
    LevelSelectController.prototype.updateSelectedLevelByPosition = function () {
        if (!this.scrollView || !this.content)
            return;
        // 获取ScrollView的中心位置（相对于ScrollView节点）
        var scrollViewCenterX = 0; // ScrollView的中心就是x=0
        // 找到最接近ScrollView中心位置的关卡
        var closestLevel = 1;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.levelNodes.length; i++) {
            var levelNode = this.levelNodes[i];
            // 将关卡节点的本地坐标转换为ScrollView坐标系
            var levelWorldPos = this.content.convertToWorldSpaceAR(levelNode.position);
            var levelScrollViewPos = this.scrollView.node.convertToNodeSpaceAR(levelWorldPos);
            // 计算关卡与ScrollView中心的距离
            var distance = Math.abs(levelScrollViewPos.x - scrollViewCenterX);
            if (distance < minDistance) {
                minDistance = distance;
                closestLevel = i + 1;
            }
        }
        // 如果选中的关卡发生变化，更新显示
        if (closestLevel !== this.currentSelectedLevel) {
            this.currentSelectedLevel = closestLevel;
            this.updateLevelDisplay();
            // 通知关卡选择变化
            if (this.onLevelSelectionChanged) {
                this.onLevelSelectionChanged(closestLevel);
            }
        }
    };
    /**
     * 修复ScrollView的Scrollbar问题
     */
    LevelSelectController.prototype.fixScrollViewScrollbar = function () {
        if (this.scrollView && this.content) {
            ScrollViewHelper_1.ScrollViewHelper.setupHorizontalScrollView(this.scrollView, this.content);
            ScrollViewHelper_1.ScrollViewHelper.debugScrollView(this.scrollView, "LevelSelectController");
        }
    };
    /**
     * 调试参数信息
     */
    LevelSelectController.prototype.debugParameters = function () {
        // 计算连接线总宽度
        var totalLineWidth = (this.lineCount - 1) * this.lineSpacing;
        // 计算可用宽度
        var availableWidth = this.levelItemWidth - 46 - (this.levelToLineDistance * 2);
        if (totalLineWidth > availableWidth) {
            var adjustedSpacing = availableWidth / (this.lineCount - 1);
        }
        else {
        }
    };
    /**
     * 更新标签外边框
     */
    LevelSelectController.prototype.updateLabelOutline = function (outline, status) {
        var outlineColor;
        switch (status) {
            case LevelStatus.LOCKED:
                // 未解锁边框为 #7B7B7B
                outlineColor = cc.color(123, 123, 123);
                break;
            case LevelStatus.CURRENT:
                // 当前玩到的关卡边框 #CF5800
                outlineColor = cc.color(207, 88, 0);
                break;
            case LevelStatus.COMPLETED:
                // 已解锁边框为 #119C0F
                outlineColor = cc.color(17, 156, 15);
                break;
            default:
                outlineColor = cc.color(123, 123, 123);
                break;
        }
        outline.color = outlineColor;
        outline.width = 1;
    };
    /**
     * 重新创建关卡项目（用于参数调整后刷新）
     */
    LevelSelectController.prototype.refreshLevelItems = function () {
        this.createLevelItems();
        this.updateLevelDisplay();
        this.scrollToLevel(this.currentSelectedLevel);
    };
    __decorate([
        property(cc.ScrollView)
    ], LevelSelectController.prototype, "scrollView", void 0);
    __decorate([
        property(cc.Node)
    ], LevelSelectController.prototype, "content", void 0);
    LevelSelectController = __decorate([
        ccclass
    ], LevelSelectController);
    return LevelSelectController;
}(cc.Component));
exports.default = LevelSelectController;

cc._RF.pop();