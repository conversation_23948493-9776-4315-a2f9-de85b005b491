"use strict";
cc._RF.push(module, 'cc55fhCTRdMjZxuQuVowyEX', 'en');
// resources/i18n/en.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.language = void 0;
exports.language = {
    //这部分是通用的
    kickout1: 'You have been asked to leave the room',
    LeaveRoom: 'The room is dissolved',
    InsufficientBalance: 'The current balance is insufficient, please go to purchase',
    GameRouteNotFound: 'Game route not found',
    NetworkError: 'network error',
    RoomIsFull: 'Room is full',
    EnterRoomNumber: 'Enter room number',
    GetUserInfoFailed: 'get user info failed',
    RoomDoesNotExist: 'Room does not exist',
    FailedToDeductGoldCoins: 'Failed to deduct gold coins',
    ExitApplication: 'Are you sure to leave?',
    QuitTheGame: 'Once you exit the game, you won’t be able to return to it.',
    NotEnoughPlayers: 'Not enough players',
    TheGameIsFullOfPlayers: 'The game is full of players',
    kickout2: 'Whether to kick {0} out of the room?',
    upSeat: 'Join',
    downSeat: 'Leave',
    startGame: 'Start',
    readyGame: 'Ready',
    cancelGame: 'Cancel',
    cancel: 'Cancel',
    confirm: 'Confirm',
    kickout3: 'Kick Out',
    back: 'Back',
    leave: 'Leave',
    music: 'Music',
    sound: 'Sound',
    join: 'Join',
    create: 'Create',
    auto: 'Auto',
    Room: 'Room',
    room_number: 'Room Number',
    copy: 'Copy',
    game_amount: 'Game Amount',
    player_numbers: 'Player Numbers:',
    room_exist: 'Room doesn’t exist',
    enter_room_number: 'Enter room number',
    free: 'Free',
    players: 'Players',
    Player: 'Player',
    Tickets: 'Tickets',
    Empty: 'Empty',
    nextlevel: 'Next',
    relevel: 'Play Again',
    rules: 'Rules',
    danjiguize: "Single Player Rules",
    lianjuguize: "Multiplayer Rules",
    dtips1: "Game Introduction:",
    dtips2: "Safe Zone:",
    dtips3: "Mine Zone:",
    dtips4: "Game Objective:",
    dtips5: "Marking:",
    dtips6: "Hint:",
    dinfo1: "The game contains hidden tiles that can be: numbered tiles, blank tiles, or mines. Click to reveal tiles - numbered tiles indicate how many mines are adjacent to that tile. Blank tiles will trigger a chain reaction of automatic reveals until numbered tiles are encountered. Use these mechanics to clear the board.",
    dinfo2: "Numbered tiles and blank tiles are collectively called the Safe Zone.",
    dinfo3: "Revealing a mine tile will cause instant failure.",
    dinfo4: "Reveal all safe tiles without triggering any mines to win.",
    dinfo5: "Long-press to place a flag marker on suspected mine tiles. Marking doesn't reveal the tile.",
    dinfo6: "Players get one free hint per game (max 4 uses). Clicking Hint will reveal one safe tile. Hints are game-specific and don't carry over.",
    ltips1: "Game Introduction:",
    ltips2: "Player Count:",
    ltips3: "Turn Duration:",
    ltips4: "Game Objective:",
    ltips5: "Marking:",
    ltips6: "Scoring Rules:",
    linfo1: "Uses the same tile mechanics as Single Player mode. Each turn, all players simultaneously select tiles. After time expires or all players finish choosing, results are revealed with score adjustments. Game ends when all tiles are claimed. Compete for the highest score!",
    linfo2: "2/3/4 Players",
    linfo3: "20 Seconds",
    linfo4: "Score points through gameplay mechanics to win.",
    linfo5: "Long-press to mark potential mines. Correct mine markings grant bonus points.",
    linfo6: "1. First player to select a tile each turn earns +1pt.\n2. Click-reveal: +6pts for safe tiles, -12pts for mines.\n3. Mark-reveal: +10pts for correctly flagged mines, 0pts for safe tiles.\n4. Shared tiles: Points are split (e.g., two players click same safe tile = +3pts each)."
};
var cocos = cc;
if (!cocos.Jou_i18n)
    cocos.Jou_i18n = {};
cocos.Jou_i18n.en = exports.language;

cc._RF.pop();