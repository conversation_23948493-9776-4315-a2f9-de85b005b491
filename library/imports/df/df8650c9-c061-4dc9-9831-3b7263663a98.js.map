{"version": 3, "sources": ["assets/resources/i18n/zh_HK.ts"], "names": [], "mappings": ";;;;;;;AAAa,QAAA,QAAQ,GAAG;IACpB,SAAS;IACT,QAAQ,EAAE,QAAQ;IAClB,SAAS,EAAE,OAAO;IAClB,mBAAmB,EAAE,UAAU;IAC/B,iBAAiB,EAAE,QAAQ;IAC3B,YAAY,EAAE,MAAM;IACpB,UAAU,EAAE,MAAM;IAClB,eAAe,EAAE,OAAO;IACxB,iBAAiB,EAAE,UAAU;IAC7B,gBAAgB,EAAE,OAAO;IACzB,uBAAuB,EAAE,QAAQ;IACjC,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,aAAa;IAE1B,gBAAgB,EAAE,QAAQ;IAC1B,sBAAsB,EAAE,QAAQ;IAChC,QAAQ,EAAE,eAAe;IAEzB,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,MAAM;IAChB,SAAS,EAAE,IAAI;IACf,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,MAAM;IAClB,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,IAAI,EAAE,IAAI;IACV,MAAM,EAAE,IAAI;IACZ,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,WAAW,EAAE,IAAI;IACjB,IAAI,EAAE,IAAI;IACV,WAAW,EAAE,MAAM;IACnB,cAAc,EAAE,OAAO;IACvB,UAAU,EAAE,OAAO;IACnB,iBAAiB,EAAE,OAAO;IAC1B,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,IAAI;IACb,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IAEX,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,MAAM;IAEf,UAAU,EAAE,MAAM;IAClB,WAAW,EAAE,MAAM;IAEnB,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IAEb,MAAM,EAAE,+GAA+G;IACvH,MAAM,EAAE,kBAAkB;IAC1B,MAAM,EAAE,eAAe;IACvB,MAAM,EAAE,8BAA8B;IACtC,MAAM,EAAE,iCAAiC;IACzC,MAAM,EAAE,oDAAoD;IAG5D,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,OAAO;IACf,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,OAAO;IAEf,MAAM,EAAE,wGAAwG;IAChH,MAAM,EAAE,UAAU;IAClB,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,uBAAuB;IAC/B,MAAM,EAAE,gCAAgC;IACxC,MAAM,EAAE,sJAAsJ;CAGjK,CAAC;AAEF,IAAM,KAAK,GAAG,EAAS,CAAC;AACxB,IAAI,CAAC,KAAK,CAAC,QAAQ;IAAE,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,gBAAQ,CAAC", "file": "", "sourceRoot": "/", "sourcesContent": ["export const language = {\n    //這部分是通用的\n    kickout1: '您被請出房間',\n    LeaveRoom: '房間已解散',\n    InsufficientBalance: '餘額不足，去儲值',\n    GameRouteNotFound: '遊戲路線異常',\n    NetworkError: '網絡異常',\n    RoomIsFull: '房間已滿',\n    EnterRoomNumber: '輸入房間號',\n    GetUserInfoFailed: '獲取用戶資訊失敗',\n    RoomDoesNotExist: '房間不存在',\n    FailedToDeductGoldCoins: '扣除金幣失敗',\n    ExitApplication: '確定退出遊戲？',\n    QuitTheGame: '退出後將無法返回遊戲。',\n\n    NotEnoughPlayers: '玩家數量不足',\n    TheGameIsFullOfPlayers: '玩家數量已滿',\n    kickout2: '是否將 {0} 請出房間?',\n\n    upSeat: '加入遊戲',\n    downSeat: '退出遊戲',\n    startGame: '開始',\n    readyGame: '準備',\n    cancelGame: '取消準備',\n    cancel: '取消',\n    confirm: '確定',\n    kickout3: '踢出',\n    back: '返回',\n    leave: '退出',\n    music: '音樂',\n    sound: '音效',\n    join: '進入', //加入\n    create: '創建', //创建\n    auto: '匹配',\n    Room: '房間',\n    room_number: '房號', //房间号\n    copy: '複製', //复制\n    game_amount: '遊戲費用', //游戏费用\n    player_numbers: '玩家數量:', //玩家数量\n    room_exist: '房間不存在',//房间不存在\n    enter_room_number: '輸入房間號',//输入房间号\n    free: '免費',\n    players: '玩家', //玩家\n    Player: '玩家',\n    Tickets: '門票',\n    Empty: '空位',\n\n    nextlevel: '下一關', //下一關  \n    relevel: '再玩一次', //再來一局 \n\n    danjiguize: \"單機規則\",\n    lianjuguize: \"聯機規則\",\n\n    dtips1: \"遊戲簡介：\",\n    dtips2: \"安全區：\",\n    dtips3: \"雷區：\",\n    dtips4: \"遊戲目標：\",\n    dtips5: \"標記：\",\n    dtips6: \"提示：\",\n\n    dinfo1: \"遊戲中存在一些格子，翻開後分為數字格子、空白格子和地雷。玩家可以通過點擊翻開新格子，數字格子可以幫助玩家獲得周圍相鄰格子中存在的地雷數量。若翻到空白格子會繼續擴散翻牌，直到翻到數字格子才停止。利用遊戲規則，順利通關吧。\",\n    dinfo2: \"數字格子與空白格子統稱為安全區。\",\n    dinfo3: \"翻到雷區，會導致遊戲失敗。\",\n    dinfo4: \"在不觸發任何雷區的情況下，揭示遊戲區域中的所有安全格子。\",\n    dinfo5: \"玩家可以通過對格子長按，插旗子來標記雷區，標記不會翻開該格子。\",\n    dinfo6: \"玩家每局可獲得一次提示，僅本局生效。點擊提示可以幫助玩家顯示出一個安全的格子。一局最多使用4次提示。\",\n\n\n    ltips1: \"遊戲簡介：\",\n    ltips2: \"遊戲人數：\",\n    ltips3: \"回合時間：\",\n    ltips4: \"遊戲目標：\",\n    ltips5: \"標記：\",\n    ltips6: \"得分規則：\",\n\n    linfo1: \"與單機模式相同，格子的樣式一致。每個回合所有玩家同時選擇任意一個格子，時間結束後或所有人全部選擇完畢後，展示所有人的選擇情況並根據格子的內容進行加減分。待所有格子都被選完，遊戲結束。爭取得到更多的分數吧！\",\n    linfo2: \"2人/3人/4人\",\n    linfo3: \"20秒\",\n    linfo4: \"利用遊戲規則進行得分，分數最高者獲得勝利。\",\n    linfo5: \"長按可對格子進行雷區標記，若該格子為雷區，則可額外獲得加分。\",\n    linfo6: \"1. 每回合最先選擇格子的玩家獲得1分。\\n2. 單擊翻開格子，若為安全格子則加6分，為雷區則扣12分。\\n3. 長按進行標記翻開格子，若為雷區則加10分，為安全區則不加分。\\n4. 多人同時選擇一個格子，根據選擇的對錯結果進行平均加分。例如兩人單擊選擇同一個格子，該格子為安全區，則每人得3分。\"\n\n\n};\n\nconst cocos = cc as any;\nif (!cocos.Jou_i18n) cocos.Jou_i18n = {};\ncocos.Jou_i18n.zh_HK = language;"]}